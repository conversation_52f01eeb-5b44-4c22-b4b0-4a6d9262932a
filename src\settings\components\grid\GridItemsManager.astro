---
import type { GridItem } from '../../types';

export interface Props {
  items: GridItem[];
  blockId: string;
  maxItems?: number;
  onUpdate?: (items: GridItem[]) => void;
}

const { items, blockId, maxItems = 12 } = Astro.props;

// Генерируем ID для новых элементов
function generateItemId(): string {
  return 'item_' + Math.random().toString(36).substr(2, 9);
}

// Создаем пустой элемент
function createEmptyItem(order: number): GridItem {
  return {
    id: generateItemId(),
    content: {
      ru: '',
      en: ''
    },
    order,
    gridColumn: undefined,
    gridRow: undefined
  };
}

// Если нет элементов, создаем один пустой
const currentItems = items.length > 0 ? items : [createEmptyItem(1)];
---

<div class="grid-items-manager" data-block-id={blockId}>
  <div class="items-header mb-4">
    <h4 class="text-md font-medium text-gray-900">Элементы Grid ({currentItems.length}/{maxItems})</h4>
    <p class="text-sm text-gray-600">Добавьте до {maxItems} элементов в вашу grid-сетку</p>
  </div>

  <!-- Список элементов -->
  <div class="items-list space-y-4">
    {currentItems.map((item, index) => (
      <div class="grid-item-editor" data-item-id={item.id}>
        <div class="item-header flex items-center justify-between mb-3">
          <h5 class="text-sm font-medium text-gray-800">Элемент {index + 1}</h5>
          <div class="item-actions flex space-x-2">
            {index > 0 && (
              <button 
                type="button"
                class="move-up-btn text-gray-500 hover:text-gray-700"
                data-item-id={item.id}
                title="Переместить вверх"
              >
                ↑
              </button>
            )}
            {index < currentItems.length - 1 && (
              <button 
                type="button"
                class="move-down-btn text-gray-500 hover:text-gray-700"
                data-item-id={item.id}
                title="Переместить вниз"
              >
                ↓
              </button>
            )}
            {currentItems.length > 1 && (
              <button 
                type="button"
                class="delete-item-btn text-red-500 hover:text-red-700"
                data-item-id={item.id}
                title="Удалить элемент"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        <!-- Контент элемента -->
        <div class="item-content mb-3">
          <div class="language-tabs mb-2">
            <button 
              type="button"
              class="lang-tab active"
              data-lang="ru"
              data-item-id={item.id}
            >
              Русский
            </button>
            <button 
              type="button"
              class="lang-tab"
              data-lang="en"
              data-item-id={item.id}
            >
              English
            </button>
          </div>

          <!-- Русский контент -->
          <div class="lang-content active" data-lang="ru" data-item-id={item.id}>
            <textarea
              name={`item-content-ru-${item.id}`}
              placeholder="Введите контент на русском языке..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="4"
            >{item.content.ru}</textarea>
          </div>

          <!-- Английский контент -->
          <div class="lang-content" data-lang="en" data-item-id={item.id} style="display: none;">
            <textarea
              name={`item-content-en-${item.id}`}
              placeholder="Enter content in English..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="4"
            >{item.content.en}</textarea>
          </div>
        </div>

        <!-- Настройки позиционирования (для профи режима) -->
        <div class="item-positioning pro-only" style="display: none;">
          <h6 class="text-xs font-medium text-gray-700 mb-2">Позиционирование в Grid</h6>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Grid Column</label>
              <input 
                type="text"
                name={`item-grid-column-${item.id}`}
                value={item.gridColumn || ''}
                placeholder="1 / 3 или span 2"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Grid Row</label>
              <input 
                type="text"
                name={`item-grid-row-${item.id}`}
                value={item.gridRow || ''}
                placeholder="1 / 2 или span 1"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    ))}
  </div>

  <!-- Кнопка добавления нового элемента -->
  {currentItems.length < maxItems && (
    <div class="add-item-section mt-4">
      <button 
        type="button"
        class="add-item-btn w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition-colors"
        data-block-id={blockId}
      >
        + Добавить элемент ({currentItems.length + 1}/{maxItems})
      </button>
    </div>
  )}

  <!-- Скрытые поля для передачи данных -->
  <input type="hidden" name="gridItemsData" value={JSON.stringify(currentItems)} />
</div>

<style>
  .grid-items-manager {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
  }

  .grid-item-editor {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .language-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
  }

  .lang-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
  }

  .lang-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
  }

  .lang-tab:hover {
    color: #1e40af;
  }

  .item-actions button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
  }

  .item-actions button:hover {
    background-color: #f3f4f6;
  }

  .add-item-btn:hover {
    background-color: #f8fafc;
  }

  /* Скрываем настройки позиционирования по умолчанию */
  .pro-only {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e5e7eb;
  }
</style>

<script>
  // Функции для управления элементами будут добавлены через отдельный JS файл
  console.log('GridItemsManager loaded');
</script>
