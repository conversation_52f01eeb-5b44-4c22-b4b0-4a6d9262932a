---
// Компонент для управления вариантами товаров
// Принимает настройки товаров и данные атрибутов как пропсы
export interface Props {
  settingsData?: any;
  attributesData?: any;
  attributeTypes?: any;
}

const { settingsData, attributesData, attributeTypes } = Astro.props;
---

<!-- Варианты товара -->
<div id="variants-section" class="mb-6">
  <h3 class="text-lg font-medium text-gray-900 mb-3">Варианты товара</h3>
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
    <div class="flex items-start">
      <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
      </svg>
      <div class="text-sm text-blue-800">
        <p class="font-medium mb-1">Как создать варианты товара:</p>
        <ol class="list-decimal list-inside space-y-1 text-blue-700">
          <li>Сначала добавьте атрибуты в секции "Атрибуты товара" выше</li>
          <li>Затем создайте варианты, используя эти атрибуты с индивидуальными ценами</li>
          <li>В вариантах будут доступны только те атрибуты, которые вы уже добавили</li>
        </ol>
      </div>
    </div>
  </div>
  <div id="variants-container" class="space-y-4">
    <!-- Варианты будут добавлены динамически -->
  </div>
  <button
    type="button"
    id="add-variant-btn"
    class="mt-3 inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
  >
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Добавить вариант
  </button>
</div>

<script define:vars={{ settingsData, attributesData, attributeTypes }} is:inline>
  // Передаем данные в глобальные переменные для использования в утилитах
  if (typeof window !== 'undefined') {
    window.variantSettingsData = settingsData;
    window.variantAttributesData = attributesData;
    window.variantAttributeTypes = attributeTypes;
  }
</script>
