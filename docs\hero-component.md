# Документация компонента Hero

## Назначение
Hero-секция — это главный визуальный блок сайта, который привлекает внимание пользователя, содержит ключевой посыл и призыв к действию.

## Особенности реализации
- Контент выровнен по левой границе контейнера (как логотип и основной контент сайта).
- Высота блока Hero всегда 50% экрана (`min-h-[50vh] max-h-[50vh]`), контент по центру по вертикали.
- Цвета и шрифты соответствуют фирменному стилю Aizen: основной цвет текста — #403e39, шрифт Titillium Web, жирный, uppercase.
- Кнопки и все блоки — без скруглений (острые края, `border-radius: 0`).
- Кнопка "Оставить заявку" — насыщенно-желтая (#c8b499), при hover — #b39f86, текст белый.
- Кнопка "Наша продукция" — белая с border #c8b499, при hover — желтая, текст белый.
- Все параметры задаются через props.

## Как изменить фон, название, описание Hero

### 1. Откройте нужную страницу
- Для главной страницы: `src/pages/index.astro`
- Для других страниц: откройте соответствующий файл в папке `src/pages/`

### 2. Найдите импорт и вызов компонента Hero
```astro
import Hero from '../components/hero/Hero.astro';
...
<Hero
  background="/images/main-slider/image-1.jpg"
  title="We create living, breathing structures that inspire"
  subtitle="Together we ensure that each building fulfills their needs and goals, as unique manifestations of brand, mission and values"
/>
```

### 3. Измените параметры:
- **background** — путь к новому изображению (например, `/images/main-slider/image-2.jpg`)
- **title** — новый заголовок (например, `title="Современная архитектура для жизни"`)
- **subtitle** — новое описание (например, `subtitle="Мы создаем пространства, которые вдохновляют и объединяют"`)

### 4. Сохраните файл и обновите страницу в браузере

Hero автоматически применит новые значения.

## Пример с двумя кнопками
В компоненте уже реализованы две кнопки:
- "Оставить заявку" (желтая, ведет на /cart)
- "Наша продукция" (белая, ведет на /products)

Обе кнопки:
- Прямоугольные, без скруглений
- Жирный текст, uppercase
- Адаптивные, всегда слева от блока
- Цвета и стили соответствуют шаблону

## Пропсы
- `background` (string, обяз.) — путь к фоновому изображению
- `title` (string, обяз.) — заголовок секции
- `subtitle` (string, необяз.) — подзаголовок/описание

## Рекомендации
- Используйте изображения высокого качества (1920x800+)
- Для мультиязычности — передавайте тексты через i18n
- Для SEO — используйте осмысленный alt для background

## Пример расширения до слайдера
Для реализации слайдера используйте массив объектов и логику переключения (Astro Islands или VanillaJS).

---

Если потребуется доработать компонент или добавить анимации — обращайтесь! 