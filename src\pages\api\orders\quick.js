import fs from 'fs/promises';
import path from 'path';

const ordersPath = path.join(process.cwd(), 'data/orders/orders-product.json');

export async function POST({ request }) {
  try {
    const body = await request.json();

    // Валидация данных быстрого заказа
    const validationErrors = validateQuickOrderRequest(body);
    if (validationErrors.length > 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Ошибки валидации данных',
        details: validationErrors
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создание объекта быстрого заказа
    const order = {
      id: generateQuickOrderId(),
      type: 'quick_order',
      product: {
        id: body.product.id,
        name: body.product.name,
        price: body.product.price,
        currency: body.product.currency,
        unit: body.product.unit
      },
      quantity: body.quantity,
      customer: {
        name: body.customer.name.trim(),
        phone: body.customer.phone
      },
      comment: body.comment ? body.comment.trim() : '',
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Чтение существующих заказов
    let ordersData;
    try {
      const data = await fs.readFile(ordersPath, 'utf-8');
      ordersData = JSON.parse(data);
    } catch (error) {
      // Если файл не существует, создаем новую структуру
      ordersData = { orders: [] };
    }

    // Добавление нового заказа
    ordersData.orders.unshift(order); // Добавляем в начало для сортировки по дате

    // Сохранение в файл
    await fs.writeFile(ordersPath, JSON.stringify(ordersData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ 
      success: true, 
      orderId: order.id,
      message: 'Заявка успешно отправлена' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при создании быстрого заказа:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при отправке заявки' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция валидации данных быстрого заказа
function validateQuickOrderRequest(data) {
  const errors = [];

  // Проверка данных товара
  if (!data.product) {
    errors.push('Отсутствуют данные товара');
  } else {
    if (!data.product.id || typeof data.product.id !== 'string') {
      errors.push('Некорректный ID товара');
    }
    if (!data.product.name || typeof data.product.name !== 'string') {
      errors.push('Некорректное название товара');
    }
    if (typeof data.product.price !== 'number' || data.product.price < 0) {
      errors.push('Некорректная цена товара');
    }
  }

  // Проверка количества
  if (!data.quantity || typeof data.quantity !== 'number' || data.quantity < 1) {
    errors.push('Некорректное количество товара');
  }

  // Проверка данных клиента
  if (!data.customer) {
    errors.push('Отсутствуют данные клиента');
  } else {
    if (!data.customer.name || typeof data.customer.name !== 'string' || data.customer.name.trim().length < 2) {
      errors.push('Имя клиента должно содержать минимум 2 символа');
    }
    if (!data.customer.phone || typeof data.customer.phone !== 'string') {
      errors.push('Некорректный номер телефона');
    } else {
      // Валидация белорусского номера телефона
      if (!validateBelarusianPhone(data.customer.phone)) {
        errors.push('Некорректный белорусский номер телефона');
      }
    }
  }

  // Проверка комментария (если есть)
  if (data.comment && (typeof data.comment !== 'string' || data.comment.length > 500)) {
    errors.push('Комментарий не должен превышать 500 символов');
  }

  return errors;
}

// Функция валидации белорусского номера телефона
function validateBelarusianPhone(phone) {
  const digitsOnly = phone.replace(/\D/g, '');
  
  if (digitsOnly.startsWith('375')) {
    return digitsOnly.length === 12;
  } else if (digitsOnly.length === 9) {
    const operatorCodes = ['25', '29', '33', '44'];
    return operatorCodes.some(code => digitsOnly.startsWith(code));
  }
  
  return false;
}

// Функция генерации ID быстрого заказа
function generateQuickOrderId() {
  // 7 случайных цифр
  const part1 = Array.from({length: 7}, () => Math.floor(Math.random() * 10)).join('');
  // 3 случайных цифры
  const part2 = Array.from({length: 3}, () => Math.floor(Math.random() * 10)).join('');
  return `Q-ORD-${part1}-${part2}`;
}
