---
export interface Props {
  mode: 'beginner' | 'pro';
  beginnerSettings?: {
    columns: number;
    gap: { value: number; unit: string };
    itemAlignment: string;
    contentAlignment: string;
  };
  proSettings?: {
    gridTemplateColumns: string;
    gridTemplateRows?: string;
    gridTemplateAreas?: string;
    gap: { value: number; unit: string };
    alignItems: string;
    justifyItems: string;
    alignContent: string;
    justifyContent: string;
  };
  items: any[];
  blockId: string;
}

const { mode, beginnerSettings, proSettings, items, blockId } = Astro.props;

// Генерируем стили для предварительного просмотра
function generateGridStyles() {
  if (mode === 'beginner' && beginnerSettings) {
    return {
      gridTemplateColumns: `repeat(${beginnerSettings.columns}, 1fr)`,
      gap: `${beginnerSettings.gap.value}${beginnerSettings.gap.unit}`,
      alignItems: beginnerSettings.itemAlignment,
      justifyContent: beginnerSettings.contentAlignment
    };
  } else if (mode === 'pro' && proSettings) {
    return {
      gridTemplateColumns: proSettings.gridTemplateColumns,
      gridTemplateRows: proSettings.gridTemplateRows || 'auto',
      gridTemplateAreas: proSettings.gridTemplateAreas || 'none',
      gap: `${proSettings.gap.value}${proSettings.gap.unit}`,
      alignItems: proSettings.alignItems,
      justifyItems: proSettings.justifyItems,
      alignContent: proSettings.alignContent,
      justifyContent: proSettings.justifyContent
    };
  }
  return {};
}

const gridStyles = generateGridStyles();

// Генерируем CSS строку
const cssString = Object.entries(gridStyles)
  .filter(([key, value]) => value && value !== 'none' && value !== 'auto')
  .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
  .join('; ');

// Элементы для превью (теперь из EditorJS)
const previewItems = items || [];
const hasItems = previewItems.length > 0;
---

<div class="grid-visualizer" data-block-id={blockId}>
  <!-- Информация о текущих настройках -->
  <div class="grid-info mb-4">
    <h5 class="text-sm font-medium text-gray-800 mb-2">Текущие настройки:</h5>
    <div class="text-xs text-gray-600 space-y-1">
      {mode === 'beginner' && beginnerSettings && (
        <>
          <div>Колонки: {beginnerSettings.columns}</div>
          <div>Отступы: {beginnerSettings.gap.value}{beginnerSettings.gap.unit}</div>
          <div>Выравнивание: {beginnerSettings.itemAlignment}</div>
          <div>Распределение: {beginnerSettings.contentAlignment}</div>
        </>
      )}
      {mode === 'pro' && proSettings && (
        <>
          <div>Grid Template Columns: {proSettings.gridTemplateColumns}</div>
          {proSettings.gridTemplateRows && (
            <div>Grid Template Rows: {proSettings.gridTemplateRows}</div>
          )}
          {proSettings.gridTemplateAreas && (
            <div>Grid Template Areas: {proSettings.gridTemplateAreas}</div>
          )}
          <div>Gap: {proSettings.gap.value}{proSettings.gap.unit}</div>
          <div>Align Items: {proSettings.alignItems}</div>
          <div>Justify Items: {proSettings.justifyItems}</div>
        </>
      )}
    </div>
  </div>

  <!-- Предварительный просмотр Grid -->
  <div class="grid-preview-wrapper">
    <div 
      class="grid-preview-grid"
      style={cssString}
      data-mode={mode}
    >
      {hasItems ? previewItems.map((item, index) => {
        // Для профи режима применяем индивидуальные настройки позиционирования
        let itemStyle = '';
        if (mode === 'pro' && item.gridColumn) {
          itemStyle += `grid-column: ${item.gridColumn}; `;
        }
        if (mode === 'pro' && item.gridRow) {
          itemStyle += `grid-row: ${item.gridRow}; `;
        }

        return (
          <div
            class="grid-preview-item"
            style={itemStyle}
            data-item-index={index}
            data-item-type={item.type}
          >
            <div class="item-number">{index + 1}</div>
            <div class="item-type">{item.type}</div>
            <div class="item-preview" id={`preview-item-${item.id}`}>
              <!-- Контент будет добавлен через JavaScript -->
            </div>
          </div>
        );
      }) : (
        <div class="no-items-message">
          <p class="text-gray-500 text-center text-sm">
            Нет элементов для отображения
          </p>
        </div>
      )}
    </div>
  </div>

  <!-- Схематичное отображение Grid Template Areas (только для профи режима) -->
  {mode === 'pro' && proSettings?.gridTemplateAreas && proSettings.gridTemplateAreas !== 'none' && (
    <div class="grid-areas-visualization mt-4">
      <h6 class="text-sm font-medium text-gray-800 mb-2">Grid Template Areas:</h6>
      <div class="areas-preview">
        <pre class="text-xs bg-gray-100 p-2 rounded border">{proSettings.gridTemplateAreas}</pre>
      </div>
    </div>
  )}

  <!-- CSS код для копирования -->
  <div class="css-output mt-4">
    <h6 class="text-sm font-medium text-gray-800 mb-2">Сгенерированный CSS:</h6>
    <div class="css-code bg-gray-900 text-green-400 p-3 rounded text-xs font-mono overflow-x-auto">
      <div>.grid-container &#123;</div>
      <div class="ml-4">display: grid;</div>
      {Object.entries(gridStyles).filter(([key, value]) => value && value !== 'none').map(([key, value]) => (
        <div class="ml-4">{key.replace(/([A-Z])/g, '-$1').toLowerCase()}: {value};</div>
      ))}
      <div>&#125;</div>
    </div>
    <button 
      type="button" 
      class="copy-css-btn mt-2 text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
      data-css={cssString}
    >
      Копировать CSS
    </button>
  </div>
</div>

<style>
  .grid-visualizer {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
  }

  .grid-preview-wrapper {
    background: white;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    padding: 1rem;
    min-height: 200px;
  }

  .grid-preview-grid {
    display: grid;
    width: 100%;
    min-height: 150px;
  }

  .grid-preview-item {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 2px solid #3b82f6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    min-height: 60px;
    transition: all 0.2s ease;
  }

  .grid-preview-item:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  .item-number {
    position: absolute;
    top: 4px;
    left: 6px;
    background: #3b82f6;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    font-weight: bold;
    z-index: 10;
  }

  .item-type {
    position: absolute;
    top: 4px;
    right: 6px;
    background: #10b981;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 8px;
    font-weight: 500;
    z-index: 10;
  }

  .item-preview {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 20px 8px 8px 8px;
  }

  .no-items-message {
    grid-column: 1 / -1;
    padding: 2rem;
    text-align: center;
  }

  .css-code {
    max-height: 200px;
    overflow-y: auto;
  }

  .copy-css-btn:hover {
    transform: translateY(-1px);
  }

  /* Специальные стили для разных режимов выравнивания */
  .grid-preview-grid[data-mode="beginner"] .grid-preview-item {
    border-color: #10b981;
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  }

  .grid-preview-grid[data-mode="beginner"] .grid-preview-item .item-number {
    background: #10b981;
  }

  .grid-preview-grid[data-mode="beginner"] .grid-preview-item .item-content {
    color: #065f46;
  }

  .grid-preview-grid[data-mode="beginner"] .grid-preview-item:hover {
    background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
  }

  /* Анимация при изменении настроек */
  .grid-preview-grid {
    transition: all 0.3s ease;
  }

  .grid-preview-item {
    transition: all 0.3s ease;
  }
</style>

<script>
  // Функция копирования CSS
  document.addEventListener('click', function(e) {
    const target = e.target;
    if (target && target.classList.contains('copy-css-btn')) {
      const css = target.dataset.css;
      if (css) {
        navigator.clipboard.writeText(`.grid-container { display: grid; ${css}; }`).then(() => {
          target.textContent = 'Скопировано!';
          setTimeout(() => {
            target.textContent = 'Копировать CSS';
          }, 2000);
        }).catch(() => {
          // Fallback для старых браузеров
          const textArea = document.createElement('textarea');
          textArea.value = `.grid-container { display: grid; ${css}; }`;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);

          target.textContent = 'Скопировано!';
          setTimeout(() => {
            target.textContent = 'Копировать CSS';
          }, 2000);
        });
      }
    }
  });

  // Функция для рендеринга элементов EditorJS в превью
  function renderEditorJSItems(items, blockId) {
    if (!window.EditorJSGridParser || !items) return;

    items.forEach((item, index) => {
      const previewElement = document.getElementById(`preview-item-${item.id}`);
      if (previewElement) {
        const html = window.EditorJSGridParser.renderGridItem(item);
        previewElement.innerHTML = html;
      }
    });
  }

  // Экспортируем функцию для использования в других скриптах
  window.renderEditorJSGridItems = renderEditorJSItems;
</script>
