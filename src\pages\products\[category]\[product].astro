---
import PageLayout from '../../../layouts/PageLayout.astro';
import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import type { Product, SettingsProduct } from '../../../types';
import formatUnit from '../../../utils/formatUnit.js';
import formatPrice from '../../../utils/formatPrice.js';
import { getMainPrice } from '../../../utils/productPricing.js';
import QuantitySelector from '../../../components/ui/QuantitySelector.astro';
import QuickOrderForm from '../../../components/ui/QuickOrderForm.astro';
import ShareBlock from '../../../components/ui/ShareBlock.astro';
import ProductTabs from '../../../components/ui/ProductTabs.astro';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Загружаем все необходимые данные в одном блоке
let attributesData = {};
let attributeTypesConfig = {};
let settingsProduct: SettingsProduct = {} as SettingsProduct;

try {
  const dataPath = path.join(__dirname, '../../../../data/product');

  const attributesFile = await fs.readFile(path.join(dataPath, 'attributes.json'), 'utf-8');
  attributesData = JSON.parse(attributesFile);

  const configPath = path.join(dataPath, 'attribute-types-config.json');
  const configFile = await fs.readFile(configPath, 'utf-8');
  attributeTypesConfig = JSON.parse(configFile);

  const settingsPath = path.join(dataPath, 'settings-product.json');
  const settingsFile = await fs.readFile(settingsPath, 'utf-8');
  settingsProduct = JSON.parse(settingsFile);

} catch (error) {
  // Ошибка загрузки данных
}

// Получаем параметры из URL
const categorySlug = Astro.params.category;
const productIdentifier = Astro.params.product; // Может быть как ID, так и SLUG

// Загружаем данные динамически
let product: Product | undefined;

try {
  const productsPath = path.join(__dirname, '../../../../data/product/products.json');
  const productsFile = await fs.readFile(productsPath, 'utf-8');
  const productsData: Product[] = JSON.parse(productsFile);

  // Находим товар по SLUG (приоритет) или ID (для обратной совместимости) и категории
  // Показываем только опубликованные товары
  product = productsData.find(p => {
    const matchesCategory = p.categorySlug === categorySlug;
    const matchesSlug = p.slug === productIdentifier;
    const matchesId = p.id === productIdentifier;
    const isPublished = p.status === 'published' || (!p.status && p.inStock);

    // Приоритет SLUG, затем ID для обратной совместимости
    return matchesCategory && (matchesSlug || (!p.slug && matchesId) || matchesId) && isPublished;
  });
} catch (error) {
  return Astro.redirect('/404');
}

if (!product) {
  return Astro.redirect('/404');
}

// Определяем главную цену товара согласно приоритетам
const mainPrice = getMainPrice(product);

// Определяем данные для корзины (основной вариант или базовый товар)
const primaryVariant = product.variants?.find(variant =>
  variant.isPrimaryPrice === true &&
  variant.price &&
  variant.price.value > 0
);

// Определяем артикул для вывода: если есть основной вариант — его sku, иначе — sku товара
const mainSku = primaryVariant?.sku || product.sku;

const cartProductData = primaryVariant ? {
  id: primaryVariant.id,
  name: `${product.name} (${primaryVariant.name})`,
  price: primaryVariant.price.value,
  currency: primaryVariant.price.currency,
  unit: primaryVariant.price.unit,
  isPrimaryVariant: true
} : {
  id: product.id,
  name: product.name,
  price: product.basePrice?.value || 0,
  currency: product.basePrice?.currency || 'BYN',
  unit: product.basePrice?.unit || 'piece',
  isPrimaryVariant: false
};

// Combine main image with additional images
const allImages = [product.images.main, ...product.images.additional];

// Функция для получения отображаемого значения атрибута
function getAttributeDisplayValue(attributeType: string, attributeValue: any, config: any) {
  if (!attributeValue) return null;

  // Универсальная обработка для объектов с полем name (приоритет)
  if (typeof attributeValue === 'object' && !Array.isArray(attributeValue) && attributeValue.name) {
    return attributeValue.name;
  }

  // Специальная обработка для некоторых типов атрибутов
  switch (attributeType) {
    case 'weight':
      if (Array.isArray(attributeValue)) {
        // Если массив весов, показываем все значения через запятую
        return attributeValue.map(w => `${w.value} ${w.unit}`).join(', ');
      } else if (typeof attributeValue === 'object' && attributeValue.value && attributeValue.unit) {
        return `${attributeValue.value} ${attributeValue.unit}`;
      } else if (typeof attributeValue === 'number') {
        return `${attributeValue} кг`;
      }
      break;

    case 'size':
      if (typeof attributeValue === 'object' && attributeValue.length && attributeValue.width && attributeValue.height) {
        // Пропускаем размеры с нулевыми значениями
        if (attributeValue.length === 0 || attributeValue.width === 0 || attributeValue.height === 0) {
          return null;
        }
        return `${attributeValue.length}×${attributeValue.width}×${attributeValue.height} мм`;
      }
      break;

    case 'texture':
    case 'surface':
    case 'pattern':
    case 'strength':
      // Простые строковые атрибуты
      if (typeof attributeValue === 'string' && attributeValue.trim()) {
        return attributeValue;
      }
      break;
  }

  // Если нет конфигурации, возвращаем простое значение
  if (!config) {
    if (Array.isArray(attributeValue)) {
      return attributeValue.join(', ');
    }
    if (typeof attributeValue === 'object') {
      return JSON.stringify(attributeValue);
    }
    return String(attributeValue);
  }

  // Для простых массивов (isSimpleArray: true)
  if (config.isSimpleArray) {
    if (Array.isArray(attributeValue)) {
      return attributeValue.join(', ');
    }
    return String(attributeValue);
  }

  // Для объектов с полями
  if (typeof attributeValue === 'object' && !Array.isArray(attributeValue)) {
    // Если есть конфигурация и формат
    if (config && config.display && config.display.format && config.display.format.trim()) {
      let formatted = config.display.format;
      if (config.fields) {
        config.fields.forEach(field => {
          const value = attributeValue[field.key];
          if (value !== undefined) {
            formatted = formatted.replace(`{${field.key}}`, value);
          }
        });
      }
      return formatted;
    }

    // Универсальная обработка объектов
    let displayFields = [];

    if (config) {
      // Если есть listView и он не пустой
      if (config.display?.listView && config.display.listView.length > 0) {
        displayFields = config.display.listView;
      }
      // Если есть поля в конфигурации
      else if (config.fields && config.fields.length > 0) {
        displayFields = config.fields.map(f => f.key);
      }
    }

    // Если нет конфигурации или полей, используем умные значения по умолчанию
    if (displayFields.length === 0) {
      // Приоритетные поля для отображения
      const priorityFields = ['name', 'title', 'label', 'value', 'description'];
      const availableFields = Object.keys(attributeValue);

      // Ищем приоритетные поля
      for (const field of priorityFields) {
        if (availableFields.includes(field) && attributeValue[field]) {
          return String(attributeValue[field]);
        }
      }

      // Если нет приоритетных полей, используем все доступные
      displayFields = availableFields;
    }

    const values = displayFields
      .map(field => attributeValue[field])
      .filter(val => val !== undefined && val !== '' && val !== null);

    return values.join(' ');
  }

  // Для массивов объектов
  if (Array.isArray(attributeValue)) {
    return attributeValue.map(item => {
      if (typeof item === 'object') {
        if (config.display && config.display.format) {
          let formatted = config.display.format;
          if (config.fields) {
            config.fields.forEach(field => {
              const value = item[field.key];
              if (value !== undefined) {
                formatted = formatted.replace(`{${field.key}}`, value);
              }
            });
          }
          return formatted;
        } else {
          const displayFields = config.display?.listView || (config.fields ? config.fields.map(f => f.key) : Object.keys(item));
          return displayFields
            .map(field => item[field])
            .filter(val => val !== undefined)
            .join(' ');
        }
      }
      return String(item);
    }).join(', ');
  }

  return String(attributeValue);
}

// Функция для получения названия атрибута
function getAttributeName(attributeType: string, config: any) {
  // Специальные названия для отображения в блоке вариантов
  const specialNames: Record<string, string> = {
    'color_pigments': 'Пигмент'
  };

  // Если есть специальное название, используем его
  if (specialNames[attributeType]) {
    return specialNames[attributeType];
  }

  // Иначе используем название из конфигурации или сам тип
  return config?.name || attributeType;
}

// Интерактивные атрибуты (размеры, цвета) - исключаем из общего списка
const interactiveAttributes = ['size', 'colors'];

// Получаем все атрибуты продукта для динамического отображения (исключая интерактивные)
const productAttributes = Object.entries(product.attributes || {})
  .filter(([key]) => !interactiveAttributes.includes(key))
  .map(([key, value]) => {
    const config = attributeTypesConfig[key];

    // Проверяем настройку отображения на странице товара
    if (config && config.showOnProductPage === false) {
      return null;
    }

    const displayValue = getAttributeDisplayValue(key, value, config);

    // Пропускаем пустые значения и значения "0"
    if (!displayValue || displayValue === '0' || displayValue === '0×0×0 мм') return null;

    return {
      key,
      name: getAttributeName(key, config),
      value: displayValue,
      config
    };
  })
  .filter(attr => attr !== null);

// === СОРТИРОВКА ВАРИАНТОВ: основной вариант всегда первый ===
const sortedVariants = product.variants
  ? [...product.variants].sort((a, b) => {
      if (a.isPrimaryPrice === b.isPrimaryPrice) return 0;
      return a.isPrimaryPrice ? -1 : 1;
    })
  : [];


---

<PageLayout
  title={product.name}
  pageTitle={product.name.toUpperCase()}
  breadcrumbs={[
    { text: 'Продукция', url: '/products' },
    { text: product.category, url: `/products/${product.categorySlug}` },
    { text: product.name, url: `/products/${product.categorySlug}/${product.slug}` }
  ]}
>
  <section class="py-16">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-10 items-start lg:flex lg:justify-between">
        <!-- Product Image Gallery -->
        <div class="product-gallery lg:w-[47.5%]" data-all-images={JSON.stringify(allImages)}>
          <div class="relative">
            <img
              id="mainImage"
              src={`/product/${allImages[0]}`}
              alt={product.name}
              class="w-full aspect-square object-cover rounded-none shadow-md mb-4"
            />
            <!-- Navigation Dots -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {allImages.map((_, index) => (
                <button
                  class="w-2 h-2 rounded-full bg-white opacity-50 hover:opacity-100 transition-opacity"
                  data-index={index}
                  aria-label={`Go to image ${index + 1}`}
                ></button>
              ))}
            </div>
          </div>

          <!-- Thumbnails -->
          <div class="grid grid-cols-4 gap-2 mb-4">
            {allImages.map((img: string, index: number) => (
              <button
                class="relative aspect-square overflow-hidden"
                data-index={index}
                aria-label={`View image ${index + 1}`}
              >
                <img
                  src={`/product/${img}`}
                  alt={`${product.name} - view ${index + 1}`}
                  class="w-full h-full object-cover rounded-none hover:opacity-75 transition-opacity"
                />
              </button>
            ))}
          </div>

          <!-- Product Full Description (только под медиа на lg+) -->
          <div class="product-full-description mt-12 pt-4 border-t border-gray-200 hidden lg:block">
            <h3 class="text-xl font-semibold mb-3">Описание</h3>
            <p class="text-gray-700 leading-relaxed mb-6">{product.fullDescription}</p>
          </div>
        </div>

        <!-- Product Details -->
        <div class="product-details lg:w-[47.5%]">
          <h1 class="text-3xl font-bold mb-4">{product.name}</h1>
          {mainSku && (
            <div class="text-sm my-2">
              <span class="text-gray-500 font-normal">Артикул товара:</span>
              <span class="text-primary font-bold">{mainSku}</span>
            </div>
          )}
          <p class="text-gray-600 mb-4 mt-4">{product.shortDescription}</p>

          <!-- Price and Action Buttons Row -->
          <div class="mt-10 mb-12 xl:mb-12">
            <div class="flex flex-col xl:flex-row items-start xl:items-center justify-between gap-4">
              <!-- Price (остается на месте всегда) -->
              <div class="flex-shrink-0 order-1 xl:order-1">
                <span class="text-2xl font-bold text-primary product-price"
                      data-main-price={mainPrice ? JSON.stringify(mainPrice) : ''}
                      data-price-source={mainPrice?.source || 'none'}>
                  {mainPrice ? formatPrice({
                    amount: mainPrice.value,
                    simvol: mainPrice.simvol || mainPrice.currency || 'BYN',
                    format: '{amount} {simvol}',
                    decimalSeparator: '.',
                    thousandsSeparator: ' ',
                    decimals: 2
                  }) : '0 BYN'}
                  {mainPrice?.unit && (
                    <span class="text-lg ml-2 text-gray-600">/ {formatUnit(mainPrice.unit, settingsProduct as any)}</span>
                  )}
                </span>
              </div>

              <!-- Селектор количества + кнопки: под ценой в одну строку при <1280px, в одной строке с ценой при >=1280px -->
              <div class="flex flex-row w-full xl:w-auto gap-3 order-2 xl:order-2 mt-2 xl:mt-0">
                <div class="flex-shrink-0">
                  <div class="quantity-selector quantity-selector-main" style="display: flex; align-items: stretch; position: relative; height: 46px; border: 1px solid #e5e7eb; background: white; font-family: inherit;">
                    <div class="quantity-display" style="display: flex; align-items: center; justify-content: center; min-width: 48px; width: 64px; background: white; height: 46px;">
                      <input type="number" id="main-qty-input" class="quantity-input-main" style="width: 100%; height: 46px; text-align: center; font-size: 19px; font-weight: 600; color: #2b2a28; border: none; outline: none; background: white; padding: 0; margin: 0; display: block; line-height: 44px; vertical-align: middle; transition: background 0.2s; box-sizing: border-box; appearance: textfield;" min="1" max="999" value="1" />
                    </div>
                    <div class="quantity-controls" style="display: flex; flex-direction: column; width: 22px; height: 46px; background: white;">
                      <button type="button" class="quantity-btn quantity-btn-plus" id="main-qty-plus" aria-label="Увеличить" style="width: 22px; height: 23px; display: flex; align-items: center; justify-content: center; border: none; background: #f3f4f6; font-size: 18px; font-weight: 400; color: #111; cursor: pointer; outline: none; transition: background 0.2s;">+</button>
                      <button type="button" class="quantity-btn quantity-btn-minus" id="main-qty-minus" aria-label="Уменьшить" style="width: 22px; height: 23px; display: flex; align-items: center; justify-content: center; border: none; background: #f3f4f6; font-size: 18px; font-weight: 400; color: #111; cursor: pointer; outline: none; line-height: 22px; transition: background 0.2s;"><span>&minus;</span></button>
                    </div>
                  </div>
                </div>
                <button
                  id="quick-order-btn"
                  class="order-btn w-full xl:w-auto min-w-[160px] py-2.5 px-6 text-base font-semibold border border-[#baa385] bg-white text-[#baa385] hover:bg-gray-50 transition-colors uppercase"
                  data-product-id={cartProductData.id}
                  data-product-sku={mainSku}
                  data-product-name={cartProductData.name}
                  data-product-price={cartProductData.price}
                  data-product-currency={cartProductData.currency}
                  data-product-unit={cartProductData.unit}
                  data-product-size={JSON.stringify(product.attributes?.size || null)}
                  data-is-primary-variant={cartProductData.isPrimaryVariant}
                >
                  ЗАКАЗАТЬ
                </button>
                <button
                  id="product-add-to-cart-btn"
                  class="order-btn w-full xl:w-auto min-w-[160px] py-2.5 px-6 text-base font-semibold bg-[#baa385] text-white hover:bg-[#a89274] transition-colors uppercase border border-transparent"
                  data-product-id={cartProductData.id}
                  data-product-name={cartProductData.name}
                  data-product-price={cartProductData.price}
                  data-product-currency={cartProductData.currency}
                  data-product-unit={cartProductData.unit}
                  data-product-image={`/product/${product.images.main}`}
                  data-product-category={product.category}
                  data-product-slug={product.slug}
                  data-is-primary-variant={cartProductData.isPrimaryVariant}
                  data-original-text="В КОРЗИНУ"
                >
                  В КОРЗИНУ
                </button>
              </div>
            </div>
          </div>

        <!-- Product Variants -->
        {product.variants && product.variants.length > 0 && (
          <div class="mt-8">
            <div class="variants-header cursor-pointer flex items-center justify-between p-3 border hover:bg-gray-50 transition-colors" id="variants-toggle">
              <div class="flex items-center space-x-2">
                <h3 class="text-lg font-semibold">Варианты</h3>
                <span class="text-lg font-semibold text-gray-800">({product.variants.length})</span>
              </div>
              <svg class="w-5 h-5 text-gray-600 transform transition-transform duration-200" id="variants-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div class="variants-content hidden mt-4" id="variants-content">
              <div class="space-y-4" id="product-variants" data-product-id={product.id}>
                <!-- Variants will be rendered here -->
                <div class="variants-container space-y-3">
                {sortedVariants.map((variant, index) => (
                  <div
                    class={`variant-option border border-gray-200 p-4 cursor-pointer hover:border-primary hover:shadow-md transition-all duration-200 bg-white${variant.isPrimaryPrice ? ' relative' : ''}`}
                    data-variant-id={variant.id}
                    data-variant-index={index}
                    data-variant-price={variant.price?.value || 0}
                    data-variant-currency={variant.price?.currency || 'BYN'}
                    data-variant-unit={variant.price?.unit || 'piece'}
                    data-is-primary={variant.isPrimaryPrice || false}
                  >
                    {variant.isPrimaryPrice && (
                      <span class="absolute top-4 right-4 text-primary font-bold text-lg whitespace-nowrap z-10">
                        {formatPrice({
                          amount: variant.price?.value || 0,
                          simvol: variant.price?.simvol || variant.price?.currency || 'BYN',
                          format: '{amount} {simvol}',
                          decimalSeparator: '.',
                          thousandsSeparator: ' ',
                          decimals: 2
                        })}
                        {variant.price?.unit && (
                          <span class="ml-1 text-primary text-base">/ {formatUnit(variant.price.unit, settingsProduct as any)}</span>
                        )}
                      </span>
                    )}
                    <div class="flex flex-col">
                      <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center">
                          <div class="w-4 h-4 border-2 border-gray-300 rounded-full mr-3 variant-radio"></div>
                          <h4 class="font-semibold text-lg">
                            {variant.name}
                            {variant.sku && (
                              <span class="variant-sku text-gray-500 text-sm font-normal"> (арт. {variant.sku})</span>
                            )}
                            {/* Не выводим цену здесь для основного варианта */}
                          </h4>
                        </div>
                        {/* Селектор количества и кнопка "В корзину" только если не основной вариант */}
                        {!variant.isPrimaryPrice && (
                          <div class="quantity-selector hidden flex items-center border border-gray-300">
                            <button type="button" class="quantity-decrease px-3 py-1 hover:bg-gray-100 transition-colors">-</button>
                            <input type="number" class="quantity-input w-16 text-center border-0 focus:outline-none" value="1" min="1" max="999">
                            <button type="button" class="quantity-increase px-3 py-1 hover:bg-gray-100 transition-colors">+</button>
                          </div>
                        )}
                      </div>

                        <!-- Variant Attributes -->
                        {variant.attributes && Object.keys(variant.attributes).length > 0 && (() => {
                          // First filter out empty/undefined attributes
                          const validAttributes = Object.entries(variant.attributes).filter(([key, value]) => {
                            return value && value !== 'undefined';
                          });

                          if (validAttributes.length === 0) return null;

                          return (
                            <div class="space-y-1 text-sm text-gray-600 mb-1">
                              {validAttributes.map(([key, value], index) => {
                                // Handle different attribute types
                                let displayValue = '';
                                if (Array.isArray(value)) {
                                  displayValue = value.join(', ');
                                } else if (typeof value === 'object' && value !== null && 'name' in value) {
                                  displayValue = value.name;
                                } else if (typeof value === 'object' && value !== null && 'length' in value && 'width' in value && 'height' in value) {
                                  displayValue = `${value.length}×${value.width}×${value.height} мм`;
                                } else if (typeof value === 'string') {
                                  displayValue = value;
                                } else {
                                  return null;
                                }

                                // Get attribute name from config
                                const attributeConfig = (attributeTypesConfig as any)[key];
                                const attributeName = getAttributeName(key, attributeConfig);

                                // Check if this is the last valid attribute
                                const isLastAttribute = index === validAttributes.length - 1;

                                return (
                                  <div class={`flex ${isLastAttribute ? 'justify-between items-center' : ''}`}>
                                    <div class="flex">
                                      <span class="w-24 font-medium capitalize">{attributeName}:</span>
                                      <span>{displayValue}</span>
                                    </div>
                                    {isLastAttribute && (
                                      /* Кнопка "В корзину" только если не основной вариант */
                                      !variant.isPrimaryPrice && (
                                        <button type="button" class="order-button invisible bg-[#baa385] text-white font-semibold rounded-none hover:bg-[#a89274] text-sm uppercase min-w-[128px]" data-original-text="В КОРЗИНУ">
                                          В КОРЗИНУ
                                        </button>
                                      )
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          );
                        })()}

                        <!-- Order button for variants without valid attributes - только если не основной вариант -->
                        {(() => {
                          if (!variant.attributes || Object.keys(variant.attributes).length === 0) return !variant.isPrimaryPrice;

                          // Check if there are any valid attributes after filtering
                          const validAttributes = Object.entries(variant.attributes).filter(([key, value]) => {
                            return value && value !== 'undefined';
                          });

                          return validAttributes.length === 0 && !variant.isPrimaryPrice;
                        })() && (
                          <div class="order-button-container flex justify-end mb-1">
                            <button type="button" class="order-button invisible bg-[#baa385] text-white font-semibold rounded-none hover:bg-[#a89274] text-sm uppercase min-w-[128px]" data-original-text="В КОРЗИНУ">
                              В КОРЗИНУ
                            </button>
                          </div>
                        )}

                      <!-- Price section moved to bottom — только если не основной вариант -->
                      {!variant.isPrimaryPrice && (
                        <div class="flex justify-between items-center mt-2 pt-3 border-t border-gray-200">
                          <div class="text-sm text-gray-600">
                            <span class="variant-unit-price font-bold">
                              {formatPrice({
                                amount: variant.price?.value || 0,
                                simvol: variant.price?.simvol || variant.price?.currency || 'BYN',
                                format: '{amount} {simvol}',
                                decimalSeparator: '.',
                                thousandsSeparator: ' ',
                                decimals: 2
                              })}
                              {variant.price?.unit && (
                                <span class="ml-1">/ {formatUnit(variant.price.unit, settingsProduct as any)}</span>
                              )}
                            </span>
                          </div>
                          <div class="text-right">
                            <div class="text-xl font-bold text-primary variant-total-price">
                              {formatPrice({
                                amount: variant.price?.value || 0,
                                simvol: variant.price?.simvol || variant.price?.currency || 'BYN',
                                format: '{amount} {simvol}',
                                decimalSeparator: '.',
                                thousandsSeparator: ' ',
                                decimals: 2
                              })}
                            </div>
                          </div>
                        </div>
                      )}
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </div>
          </div>
        )}

        <!-- Product Characteristics -->
        {productAttributes.length > 0 && (
          <div class="mt-4">
            <div class="characteristics-header cursor-pointer flex items-center justify-between p-3 border hover:bg-gray-50 transition-colors" id="characteristics-toggle">
              <div class="flex items-center space-x-2">
                <h3 class="text-lg font-semibold">Характеристики</h3>
                <span class="text-lg font-semibold text-gray-800">({productAttributes.length})</span>
              </div>
              <svg class="w-5 h-5 text-gray-600 transform transition-transform duration-200" id="characteristics-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div class="characteristics-content hidden mt-4" id="characteristics-content">
              <div class="space-y-3">
                {productAttributes.map(attr => (
                  <div class="flex items-start">
                    <span class="w-32 text-gray-600 flex-shrink-0">{attr.name}:</span>
                    <span class="flex-1">{attr.value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <!-- Share Block -->
        <ShareBlock
          url={Astro.url.href}
          title={product.name}
          description={product.fullDescription}
        />
        <ProductTabs />
      </div>
    </div> <!-- This closes the "grid grid-cols-1 lg:grid-cols-2 gap-10 items-start lg:flex lg:justify-between" -->

    <!-- Zoom Modal -->
    <div id="zoomModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50 hidden">
      <div class="relative bg-white p-2 rounded-none shadow-xl max-w-full max-h-full">
        <img id="zoomedImage" src="" alt="Zoomed product image" class="block max-w-full max-h-[90vh] object-contain"/>
        <button id="closeZoomModal" class="absolute top-4 right-4 w-10 h-10 bg-primary text-white flex items-center justify-center text-2xl leading-none rounded-none hover:bg-primary-dark transition-colors">&times;</button>
      </div>
    </div>

    <!-- Quick Order Form Modal -->
    <QuickOrderForm
      productId={product.id}
      productSku={product.sku}
      productName={product.name}
      productPrice={product.basePrice?.value || 0}
      productCurrency={product.basePrice?.currency || 'BYN'}
      productUnit={product.basePrice?.unit || 'piece'}
      productSize={product.attributes?.size || null}
      isPhysical={!!(product.attributes?.size)}
    />
  </section>
  <!-- Описание для мобильных и планшетов -->
  <div class="container mx-auto px-4">
    <div class="product-full-description mt-10 pt-8 border-t border-gray-200 block lg:hidden">
      <h3 class="text-xl font-semibold mb-3">Описание</h3>
      <p class="text-gray-700 leading-relaxed mb-6">{product.fullDescription}</p>
    </div>
  </div>
</PageLayout>

<style>
  /* Ensure modal is hidden by default */
  #zoomModal.hidden {
    display: none;
  }
</style>

<script define:vars={{ product }} is:inline>
  // Определяем текущий товар для использования в корзине
  window.currentProduct = {
    id: product.id,
    name: product.name,
    images: {
      main: product.images.main
    },
    category: product.category,
    slug: product.slug
  };
</script>

<script>
  // Импорт утилит ценообразования
  import { getMainPrice, shouldUpdateMainPrice, updateVariantPrice } from '../../../utils/productPricing.js';

  // Image Gallery Functionality
  const mainImageElement = document.getElementById('mainImage') as HTMLImageElement;
  const galleryElement = document.querySelector('.product-gallery') as HTMLElement;

  if (galleryElement && mainImageElement) {
    const allImageUrls: string[] = JSON.parse(galleryElement.dataset.allImages || '[]');

    // Specific selectors for image thumbnails and dot buttons
    const imageThumbnailButtons = galleryElement.querySelectorAll<HTMLButtonElement>('.grid button[data-index]');
    const dotButtons = galleryElement.querySelectorAll<HTMLButtonElement>('.relative .flex button[data-index]');

    let currentIndex = 0;

    function updateMainImageUI(newIndex: number) {
      if (newIndex < 0 || newIndex >= allImageUrls.length) {
        return;
      }

      mainImageElement.src = `/product/${allImageUrls[newIndex]}`;
      currentIndex = newIndex;

      // Update active states for image thumbnails
      imageThumbnailButtons.forEach((thumb) => {
        const thumbIndex = parseInt(thumb.dataset.index!);
        if (thumbIndex === newIndex) {
          thumb.classList.add('ring-2', 'ring-primary');
        } else {
          thumb.classList.remove('ring-2', 'ring-primary');
        }
      });

      // Update active states for dot buttons
      dotButtons.forEach((dot) => {
        const dotIndex = parseInt(dot.dataset.index!);
        if (dotIndex === newIndex) {
          dot.classList.add('opacity-100');
          dot.classList.remove('opacity-50');
        } else {
          dot.classList.add('opacity-50');
          dot.classList.remove('opacity-100');
        }
      });
    }

    // Add click handlers to image thumbnails
    imageThumbnailButtons.forEach((thumb) => {
      thumb.addEventListener('click', () => {
        const newIndex = parseInt(thumb.dataset.index!);
        if (!isNaN(newIndex)) {
          updateMainImageUI(newIndex);
        }
      });
    });

    // Add click handlers to dot buttons
    dotButtons.forEach((dot) => {
      dot.addEventListener('click', () => {
        const newIndex = parseInt(dot.dataset.index!);
        if (!isNaN(newIndex)) {
          updateMainImageUI(newIndex);
        }
      });
    });

    const zoomModal = document.getElementById('zoomModal') as HTMLElement;
    const zoomedImage = document.getElementById('zoomedImage') as HTMLImageElement;
    const closeZoomModalButton = document.getElementById('closeZoomModal') as HTMLButtonElement;

    mainImageElement.addEventListener('click', () => {
      if (zoomedImage && zoomModal && mainImageElement.src) {
        zoomedImage.src = mainImageElement.src;
        zoomModal.classList.remove('hidden');
      }
    });

    closeZoomModalButton?.addEventListener('click', () => {
      zoomModal?.classList.add('hidden');
    });

    zoomModal?.addEventListener('click', (e) => {
      // Close modal if background is clicked (but not the image itself or close button area)
      if (e.target === zoomModal) {
        zoomModal.classList.add('hidden');
      }
    });

    // Swipe functionality for touch devices
    let touchstartX = 0;
    let touchendX = 0;

    function handleSwipe() {
      if (touchendX < touchstartX - 50) { // Swiped left
        if (allImageUrls.length > 0) {
          const nextIndex = (currentIndex + 1) % allImageUrls.length;
          updateMainImageUI(nextIndex);
        }
      }
      if (touchendX > touchstartX + 50) { // Swiped right
        if (allImageUrls.length > 0) {
          const prevIndex = (currentIndex - 1 + allImageUrls.length) % allImageUrls.length;
          updateMainImageUI(prevIndex);
        }
      }
    }

    mainImageElement.addEventListener('touchstart', e => {
      touchstartX = e.changedTouches[0].screenX;
    }, { passive: true });

    mainImageElement.addEventListener('touchend', e => {
      touchendX = e.changedTouches[0].screenX;
      // Check if it's a touch device before handling swipe
      if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
          handleSwipe();
      }
    });

    // Initialize first image if available
    if (allImageUrls.length > 0) {
      updateMainImageUI(0);
    } else {
      mainImageElement.style.display = 'none';
      const dotsContainer = galleryElement.querySelector('.relative .flex');
      const thumbnailsContainer = galleryElement.querySelector('.grid');
      if(dotsContainer) (dotsContainer as HTMLElement).style.display = 'none';
      if(thumbnailsContainer) (thumbnailsContainer as HTMLElement).style.display = 'none';
    }
  }

  // Динамическое изменение цены при выборе параметров
  const priceElement = document.querySelector('.product-price');
  const basePrice = parseFloat(priceElement?.textContent?.replace(/[^0-9.,]/g, '').replace(',', '.') || '0');
  const sizeButtons = document.querySelectorAll<HTMLButtonElement>('.size-variant-btn');
  const colorButtons = document.querySelectorAll<HTMLButtonElement>('.color-variant-btn');

  let selectedSize: { length: number; width: number; height: number; index: number } | null = null;
  let selectedColor: { color: string; index: number } | null = null;

  // Коэффициенты изменения цены
  const colorPriceFactors: Record<string, number> = {
    'серый': 1.0,      // без пигментов - базовая цена
    'красный': 1.15,   // 1 пигмент
    'желтый': 1.15,    // 1 пигмент
    'коричневый': 1.15, // 1 пигмент
    'красно/желтый': 1.25, // 2 пигмента
    'синий/зеленый': 1.25, // 2 пигмента
    'уникальное сочетание': 1.4 // 3 пигмента
  };

  // Функция обновления цены
  function updatePrice() {
    if (!priceElement) return;

    let newPrice = basePrice;

    // Применяем коэффициент размера (если выбран)
    if (selectedSize) {
      // Здесь можно добавить логику изменения цены в зависимости от размера
      // Например, увеличение цены для больших размеров
      const volume = selectedSize.length * selectedSize.width * selectedSize.height;
      const baseVolume = 200 * 100 * 40; // базовый объем для примера
      newPrice = newPrice * (volume / baseVolume);
    }

    // Применяем коэффициент цвета (если выбран)
    if (selectedColor && colorPriceFactors[selectedColor.color]) {
      newPrice = newPrice * colorPriceFactors[selectedColor.color];
    }

    // Обновляем отображение цены
    const formattedPrice = newPrice.toFixed(2).replace('.', ',');
    const unit = priceElement.getAttribute('data-unit') || 'шт';
    priceElement.textContent = `${formattedPrice} ₽/${unit}`;
  }

  // Обработчики для кнопок размера
  sizeButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Сбрасываем активное состояние у всех кнопок размера
      sizeButtons.forEach(btn => {
        btn.classList.remove('border-primary', 'bg-primary-50');
        btn.classList.add('border-gray-300');
      });

      // Устанавливаем активное состояние для выбранной кнопки
      button.classList.add('border-primary', 'bg-primary-50');
      button.classList.remove('border-gray-300');

      // Сохраняем выбранный размер
      selectedSize = {
        length: parseInt(button.dataset.length || '0'),
        width: parseInt(button.dataset.width || '0'),
        height: parseInt(button.dataset.height || '0'),
        index: parseInt(button.dataset.index || '0')
      };

      // Обновляем цену
      updatePrice();
    });
  });

  // Обработчики для кнопок цвета
  colorButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Сбрасываем активное состояние у всех кнопок цвета
      colorButtons.forEach(btn => {
        btn.classList.remove('border-primary', 'bg-primary-50');
        btn.classList.add('border-gray-300');
      });

      // Устанавливаем активное состояние для выбранной кнопки
      button.classList.add('border-primary', 'bg-primary-50');
      button.classList.remove('border-gray-300');

      // Сохраняем выбранный цвет
      selectedColor = {
        color: button.dataset.color || '',
        index: parseInt(button.dataset.index || '0')
      };

      // Обновляем цену
      updatePrice();
    });
  });

  // Устанавливаем первый размер и цвет как выбранные по умолчанию, если они есть
  if (sizeButtons.length > 0) {
    sizeButtons[0].click();
  }

  if (colorButtons.length > 0) {
    colorButtons[0].click();
  }

  // === VARIANTS FUNCTIONALITY ===
  const variantOptions = document.querySelectorAll<HTMLElement>('.variant-option');
  const productPriceElement = document.querySelector('.product-price');

  if (variantOptions.length > 0) {
    let selectedVariant: HTMLElement | null = null;

    // Find default variant (primary price or first variant)
    const primaryVariant = Array.from(variantOptions).find(option =>
      option.getAttribute('data-is-primary') === 'true'
    );
    const defaultVariant = primaryVariant || variantOptions[0];

    // Function to select a variant
    function selectVariant(variantElement: HTMLElement) {
      // Remove selection from all variants
      variantOptions.forEach(option => {
        option.classList.remove('border-primary', 'bg-primary/5', 'shadow-md');
        option.classList.add('border-gray-200');
        // Update radio button
        const radio = option.querySelector('.variant-radio');
        if (radio) {
          radio.classList.remove('bg-primary', 'border-primary');
          radio.classList.add('border-gray-300');
        }
        // Hide quantity selector and order button
        const quantitySelector = option.querySelector('.quantity-selector');
        const orderButtons = option.querySelectorAll('.order-button');
        if (quantitySelector) {
          quantitySelector.classList.add('hidden');
        }
        orderButtons.forEach(orderButton => {
          orderButton.classList.add('invisible');
          orderButton.classList.remove('visible');
        });
      });

      // Add selection to current variant
      variantElement.classList.remove('border-gray-200');
      variantElement.classList.add('border-primary', 'bg-primary/5', 'shadow-md');

      // Update radio button
      const radio = variantElement.querySelector('.variant-radio');
      if (radio) {
        radio.classList.remove('border-gray-300');
        radio.classList.add('bg-primary', 'border-primary');
      }

      // Show quantity selector and order button for selected variant
      const quantitySelector = variantElement.querySelector('.quantity-selector');
      const orderButtons = variantElement.querySelectorAll('.order-button');
      if (quantitySelector) {
        quantitySelector.classList.remove('hidden');
      }
      orderButtons.forEach(orderButton => {
        orderButton.classList.remove('invisible');
        orderButton.classList.add('visible');

        // Обновляем состояние кнопки в зависимости от того, есть ли товар в корзине
        if (window.cartManager) {
          const productId = variantElement.closest('[data-product-id]')?.dataset.productId;
          const variantId = variantElement.dataset.variantId;

          if (productId && variantId && window.cartManager.isProductInCart(productId, {id: variantId})) {
            window.cartManager.setButtonAddedState(orderButton);
          } else {
            window.cartManager.setButtonNormalState(orderButton);
          }
        }
      });

      selectedVariant = variantElement;

      // Обновляем отображение цены варианта (НЕ главную цену товара)
      // Главная цена остается неизменной и показывает цену основного варианта или базовую цену
      updateVariantPrice(variantElement);
    }

    // Local function to format units in JavaScript context
    function formatUnitLocal(unit: string): string {
      if (!unit) return '';

      const unitMap: Record<string, string> = {
        // Вес
        'kg': 'кг',
        'g': 'г',
        'lb': 'фунт',
        'oz': 'унция',

        // Объем
        'L': 'л',
        'mL': 'мл',
        'm3': 'м³',
        'gal': 'галлон',

        // Размеры
        'm': 'м',
        'cm': 'см',
        'mm': 'мм',
        'm2': 'м²',

        // Штучные
        'piece': 'шт',
        'pack': 'упаковка',
        'set': 'набор',

        // Услуги
        'hour': 'час',
        'day': 'день',
        'month': 'месяц',
        'project': 'проект'
      };

      return unitMap[unit] || unit;
    }

    // Function to update price based on selected variant and quantity
    function updateVariantPrice(variantElement: HTMLElement) {
      const unitPrice = parseFloat(variantElement.getAttribute('data-variant-price') || '0');
      const currency = variantElement.getAttribute('data-variant-currency') || 'BYN';
      const unit = variantElement.getAttribute('data-variant-unit') || 'piece';
      const isPrimary = variantElement.getAttribute('data-is-primary') === 'true';

      // Get quantity from the quantity input
      const quantityInput = variantElement.querySelector('.quantity-input') as HTMLInputElement;
      const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;

      // Calculate total price
      const totalPrice = unitPrice * quantity;

      // Update variant's total price display
      const variantTotalPrice = variantElement.querySelector('.variant-total-price');
      if (variantTotalPrice) {
        const formattedTotalPrice = new Intl.NumberFormat('ru-RU', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(totalPrice);

        const currencySymbol = currency === 'BYN' ? 'BYN' :
                          currency === 'USD' ? '$' :
                          currency === 'EUR' ? '€' : currency;

        // Format unit for display
        const formattedUnit = formatUnitLocal(unit);
        // Если выбрано больше 1 — не показываем единицу измерения
        const unitDisplay = (quantity === 1 && formattedUnit) ? ` / ${formattedUnit}` : '';

        variantTotalPrice.innerHTML = `${formattedTotalPrice} ${currencySymbol}${unitDisplay}`;
      }
    }

    // Add click handlers to variant options
    variantOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        // Don't trigger variant selection if clicking on quantity selector or order button
        if ((e.target as HTMLElement).closest('.quantity-selector') ||
            (e.target as HTMLElement).closest('.order-button')) {
          return;
        }
        selectVariant(option);
      });

      // Add quantity control handlers
      const quantityInput = option.querySelector('.quantity-input') as HTMLInputElement;
      const decreaseBtn = option.querySelector('.quantity-decrease');
      const increaseBtn = option.querySelector('.quantity-increase');

      if (quantityInput && decreaseBtn && increaseBtn) {
        // Decrease quantity
        decreaseBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          const currentValue = parseInt(quantityInput.value) || 1;
          if (currentValue > 1) {
            quantityInput.value = (currentValue - 1).toString();
            updateVariantPrice(option);
          }
        });

        // Increase quantity
        increaseBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          const currentValue = parseInt(quantityInput.value) || 1;
          if (currentValue < 999) {
            quantityInput.value = (currentValue + 1).toString();
            updateVariantPrice(option);
          }
        });

        // Handle direct input
        quantityInput.addEventListener('input', () => {
          let value = parseInt(quantityInput.value) || 1;
          if (value < 1) value = 1;
          if (value > 999) value = 999;
          quantityInput.value = value.toString();
          updateVariantPrice(option);
        });
      }

      // Add order button handlers
      const orderButtons = option.querySelectorAll('.order-button');
      orderButtons.forEach(orderButton => {
        orderButton.addEventListener('click', (e) => {
          e.stopPropagation();
          const variantId = option.getAttribute('data-variant-id');
          const quantity = parseInt((option.querySelector('.quantity-input') as HTMLInputElement)?.value || '1');

          // Добавляем вариант товара в корзину
          const variantData = {
            id: variantId,
            name: `${window.currentProduct.name} (${option.querySelector('h4')?.textContent || 'Вариант'})`,
            price: parseFloat(option.getAttribute('data-variant-price') || '0'),
            currency: option.getAttribute('data-variant-currency') || 'BYN',
            image: `/product/${window.currentProduct.images.main}`,
            category: window.currentProduct.category,
            slug: window.currentProduct.slug,
            variant: {
              id: variantId,
              name: option.querySelector('h4')?.textContent || 'Вариант',
              price: parseFloat(option.getAttribute('data-variant-price') || '0'),
              currency: option.getAttribute('data-variant-currency') || 'BYN',
              unit: option.getAttribute('data-variant-unit') || 'piece'
            },
            quantity: quantity
          };

          // Добавляем в корзину если менеджер доступен
          if (window.cartManager) {
            window.cartManager.addToCart(variantData);

            // Визуальная обратная связь
            const originalText = orderButton.textContent;
            orderButton.textContent = 'Добавлено!';
            orderButton.style.background = '#10b981';

            setTimeout(() => {
              orderButton.textContent = originalText;
              orderButton.style.background = '#baa385';
            }, 1500);
          }
        });
      });
    });

    // Select default variant on page load
    // Примечание: это НЕ изменяет главную цену товара,
    // а только выбирает вариант по умолчанию для отображения
    if (defaultVariant) {
      selectVariant(defaultVariant);
    }
  }

  // Variants collapsible functionality
  const variantsToggle = document.getElementById('variants-toggle');
  const variantsContent = document.getElementById('variants-content');
  const variantsArrow = document.getElementById('variants-arrow');

  if (variantsToggle && variantsContent && variantsArrow) {
    variantsToggle.addEventListener('click', () => {
      const isHidden = variantsContent.classList.contains('hidden');

      if (isHidden) {
        // Show content
        variantsContent.classList.remove('hidden');
        variantsArrow.style.transform = 'rotate(180deg)';
        // Remove bottom border when expanded
        variantsToggle.style.borderBottom = 'none';
      } else {
        // Hide content
        variantsContent.classList.add('hidden');
        variantsArrow.style.transform = 'rotate(0deg)';
        // Restore bottom border when collapsed
        variantsToggle.style.borderBottom = '';
      }
    });
  }

  // Characteristics collapsible functionality
  const characteristicsToggle = document.getElementById('characteristics-toggle');
  const characteristicsContent = document.getElementById('characteristics-content');
  const characteristicsArrow = document.getElementById('characteristics-arrow');

  if (characteristicsToggle && characteristicsContent && characteristicsArrow) {
    characteristicsToggle.addEventListener('click', () => {
      const isHidden = characteristicsContent.classList.contains('hidden');

      if (isHidden) {
        // Show content
        characteristicsContent.classList.remove('hidden');
        characteristicsArrow.style.transform = 'rotate(180deg)';
        // Remove bottom border when expanded
        characteristicsToggle.style.borderBottom = 'none';
      } else {
        // Hide content
        characteristicsContent.classList.add('hidden');
        characteristicsArrow.style.transform = 'rotate(0deg)';
        // Restore bottom border when collapsed
        characteristicsToggle.style.borderBottom = '';
      }
    });
  }
</script>

<style>
  /* Variant selection styles */
  .variant-option {
    position: relative;
    transition: all 0.2s ease-in-out;
  }

  .variant-option:hover {
    transform: translateY(-1px);
  }

  .variant-radio {
    position: relative;
    transition: all 0.2s ease-in-out;
  }

  /* Removed white dot from selected radio button */

  /* Smooth price transition */
  .product-price {
    transition: all 0.3s ease-in-out;
  }

  /* Variant attributes styling */
  .variant-option .space-y-1 > div {
    padding: 2px 0;
  }

  .variant-option .space-y-1 span:first-child {
    color: #6b7280;
    font-weight: 500;
  }

  .variant-option .space-y-1 span:last-child {
    color: #374151;
  }

  /* Quantity selector styles */
  .quantity-selector {
    overflow: hidden;
  }

  .quantity-selector button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    user-select: none;
    background-color: #f3f4f6; /* Default gray background */
    transition: background-color 0.2s ease;
  }

  .quantity-selector button:hover {
    background-color: #e5e7eb;
  }

  .quantity-selector button:active {
    background-color: #c8b499; /* Primary color on click - same as checkbox */
    color: white;
  }

  .quantity-input {
    height: 32px;
    font-size: 14px;
    font-weight: 500;
  }

  .quantity-input::-webkit-outer-spin-button,
  .quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .quantity-input[type=number] {
    -moz-appearance: textfield;
  }

  /* Variant controls container */
  .variant-controls {
    flex-shrink: 0;
  }

  /* Order button styles - match quantity selector dimensions exactly */
  .order-button {
    height: 32px;
    width: 128px; /* Exact width as quantity selector (32px + 64px + 32px) */
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 0;
    font-size: 14px;
    border: 1px solid transparent;
    background: #baa385;
    transition: background 0.2s;
  }
  .order-button:hover {
    background: #a89274 !important;
  }
  .quantity-btn:hover {
    background: #e5e7eb !important;
  }
  .quantity-btn:active {
    background: #d1d5db !important;
  }
  /* Скрыть стрелки у input[type=number] во всех браузерах */
  .quantity-input-main::-webkit-outer-spin-button,
  .quantity-input-main::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .quantity-input-main[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
  }
  /* Эффект затемнения при фокусе/active на input */
  .quantity-display,
  .quantity-input-main {
    transition: background 0.2s;
  }
  .quantity-input-main:focus,
  .quantity-input-main:active {
    background: #e5e7eb !important;
  }
  .quantity-input-main:focus {
    outline: none;
  }
  .quantity-input-main::selection {
    background: #e5e7eb;
    color: inherit;
  }
  .quantity-input:focus,
  .quantity-input:active {
    background: #e5e7eb !important;
  }
  .quantity-input::selection {
    background: #e5e7eb;
    color: inherit;
  }

  /* Адаптив для кнопок заказа и корзины: максимально компактный вид при 1300-1535px */
  @media (min-width: 1280px) and (max-width: 1535px) {
    #quick-order-btn,
    #product-add-to-cart-btn {
      padding-left: 18px !important;
      padding-right: 18px !important;
      min-width: 80px !important;
      font-size: 14px !important;
    }
  }

  /* Артикул под названием варианта при 1024-1279px */
  @media (min-width: 1024px) and (max-width: 1279px) {
    .variant-sku {
      display: block;
      margin-left: 0;
      margin-top: 2px;
      font-size: 12px;
    }
  }
</style>

<script>
  // Обработчик для основной кнопки "В корзину"
  document.addEventListener('DOMContentLoaded', () => {
    const mainAddToCartBtn = document.getElementById('product-add-to-cart-btn');

    if (mainAddToCartBtn) {
      mainAddToCartBtn.addEventListener('click', (e) => {
        e.preventDefault();

        const isPrimaryVariant = mainAddToCartBtn.dataset.isPrimaryVariant === 'true';
        // Получаем актуальное количество из input
        const qtyInput = document.getElementById('main-qty-input') as HTMLInputElement | null;
        const quantity = qtyInput ? (parseInt(qtyInput.value) || 1) : 1;

        const productData = {
          id: mainAddToCartBtn.dataset.productId,
          name: mainAddToCartBtn.dataset.productName,
          price: parseFloat(mainAddToCartBtn.dataset.productPrice) || 0,
          currency: mainAddToCartBtn.dataset.productCurrency || 'BYN',
          unit: mainAddToCartBtn.dataset.productUnit || 'piece',
          image: mainAddToCartBtn.dataset.productImage,
          category: mainAddToCartBtn.dataset.productCategory,
          slug: mainAddToCartBtn.dataset.productSlug,
          quantity: quantity
        };

        // Если это основной вариант, добавляем информацию о варианте
        if (isPrimaryVariant) {
          productData.variant = {
            id: mainAddToCartBtn.dataset.productId,
            name: mainAddToCartBtn.dataset.productName.split(' (')[1]?.replace(')', '') || 'Основной вариант',
            price: parseFloat(mainAddToCartBtn.dataset.productPrice) || 0,
            currency: mainAddToCartBtn.dataset.productCurrency || 'BYN',
            unit: mainAddToCartBtn.dataset.productUnit || 'piece'
          };
        }

        // Добавляем товар в корзину если менеджер корзины доступен
        if (window.cartManager) {
          window.cartManager.addToCart(productData);

          // Визуальная обратная связь
          const originalText = mainAddToCartBtn.textContent;
          mainAddToCartBtn.textContent = 'Добавлено!';
          mainAddToCartBtn.style.background = '#10b981';

          setTimeout(() => {
            mainAddToCartBtn.textContent = originalText;
            mainAddToCartBtn.style.background = '#baa385';
          }, 1500);
        } else {
          // Fallback: перенаправляем на страницу заказа
          window.location.href = '/cart';
        }
      });
    }

    // Обработчик для кнопки "ЗАКАЗАТЬ"
    const quickOrderBtn = document.getElementById('quick-order-btn');
    if (quickOrderBtn && window.quickOrderModal) {
      quickOrderBtn.addEventListener('click', (e) => {
        e.preventDefault();

        // Получаем актуальное количество из input
        const qtyInput = document.getElementById('main-qty-input') as HTMLInputElement | null;
        const quantity = qtyInput ? (parseInt(qtyInput.value) || 1) : 1;

        const isPrimaryVariant = quickOrderBtn.dataset.isPrimaryVariant === 'true';

        // Подготавливаем данные товара
        const productData = {
          id: quickOrderBtn.dataset.productId,
          sku: quickOrderBtn.dataset.productSku,
          name: quickOrderBtn.dataset.productName,
          price: parseFloat(quickOrderBtn.dataset.productPrice) || 0,
          currency: quickOrderBtn.dataset.productCurrency || 'BYN',
          unit: quickOrderBtn.dataset.productUnit || 'piece',
          size: quickOrderBtn.dataset.productSize ? JSON.parse(quickOrderBtn.dataset.productSize) : null,
          quantity: quantity
        };

        // Если это основной вариант, добавляем информацию о варианте
        if (isPrimaryVariant) {
          productData.variant = {
            id: quickOrderBtn.dataset.productId,
            name: quickOrderBtn.dataset.productName.split(' (')[1]?.replace(')', '') || 'Основной вариант',
            price: parseFloat(quickOrderBtn.dataset.productPrice) || 0,
            currency: quickOrderBtn.dataset.productCurrency || 'BYN',
            unit: quickOrderBtn.dataset.productUnit || 'piece'
          };
        }

        // Открываем модальное окно
        window.quickOrderModal.open(productData);
      });
    }
  });

  // === MAIN PRODUCT QUANTITY & PRICE (custom input) ===
  document.addEventListener('DOMContentLoaded', () => {
    const priceElement = document.querySelector('.product-price');
    const minusBtn = document.getElementById('main-qty-minus') as HTMLButtonElement | null;
    const plusBtn = document.getElementById('main-qty-plus') as HTMLButtonElement | null;
    const input = document.getElementById('main-qty-input') as HTMLInputElement | null;
    let mainPriceValue = 0;
    let mainPriceCurrency = 'BYN';
    let mainPriceUnit = '';
    try {
      const mainPriceData = priceElement?.getAttribute('data-main-price');
      if (mainPriceData) {
        const parsed = JSON.parse(mainPriceData);
        mainPriceValue = parsed.value || 0;
        mainPriceCurrency = parsed.simvol || parsed.currency || 'BYN';
        mainPriceUnit = parsed.unit || '';
      }
    } catch {}

    // Выделять всё значение при фокусе
    if (input) {
      input.addEventListener('focus', (e) => {
        (e.target as HTMLInputElement).select();
      });
    }

    function formatMainPrice(amount, currency, unit, quantity) {
      const formatted = amount.toLocaleString('ru-RU', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
      if (quantity === 1) {
        return `${formatted} ${currency}${unit ? ' / ' + formatUnitLocal(unit) : ''}`;
      } else {
        return `${formatted} ${currency}`;
      }
    }
    function formatUnitLocal(unit) {
      if (!unit) return '';
      const unitMap = {
        'kg': 'кг', 'g': 'г', 'lb': 'фунт', 'oz': 'унция',
        'L': 'л', 'mL': 'мл', 'm3': 'м³', 'gal': 'галлон',
        'm': 'м', 'cm': 'см', 'mm': 'мм', 'm2': 'м²',
        'piece': 'шт', 'pack': 'упаковка', 'set': 'набор',
        'hour': 'час', 'day': 'день', 'month': 'месяц', 'project': 'проект'
      };
      return unitMap[unit] || unit;
    }
    function updatePrice() {
      if (!input) return;
      let quantity = parseInt(input.value) || 1;
      if (quantity < 1) quantity = 1;
      if (quantity > 999) quantity = 999;
      input.value = String(quantity);
      const total = mainPriceValue * quantity;
      if (priceElement) priceElement.textContent = formatMainPrice(total, mainPriceCurrency, mainPriceUnit, quantity);
      if (minusBtn) minusBtn.disabled = quantity <= 1;
      if (plusBtn) plusBtn.disabled = quantity >= 999;
    }
    if (minusBtn && plusBtn && input) {
      minusBtn.addEventListener('click', () => {
        let val = parseInt(input.value) || 1;
        if (val > 1) {
          input.value = String(val - 1);
          updatePrice();
        }
      });
      plusBtn.addEventListener('click', () => {
        let val = parseInt(input.value) || 1;
        if (val < 999) {
          input.value = String(val + 1);
          updatePrice();
        }
      });
      input.addEventListener('input', () => {
        updatePrice();
      });
      input.addEventListener('blur', () => {
        updatePrice();
      });
      updatePrice();
    }
  });

  // Добавляю выделение всего значения при фокусе для всех input[type=number] селекторов количества
  const qtyInputs = document.querySelectorAll('.quantity-selector input[type=number], .quantity-input, .cart-quantity-input');
  qtyInputs.forEach(input => {
    input.addEventListener('focus', (e) => {
      e.target.select();
    });
  });

  // === ЭФФЕКТ: quantity-input очищается при фокусе, восстанавливается при blur ===
  document.addEventListener('DOMContentLoaded', () => {
    const variantQtyInputs = document.querySelectorAll('.quantity-input');
    variantQtyInputs.forEach(input => {
      // Удаляю placeholder
      input.removeAttribute('placeholder');
      // Сохраняем предыдущее значение
      input.addEventListener('focus', function (e) {
        const el = e.target;
        if (!el.hasAttribute('data-prev')) {
          el.setAttribute('data-prev', el.value);
        }
        el.value = '';
      });
      input.addEventListener('blur', function (e) {
        const el = e.target;
        if (el.value === '' && el.hasAttribute('data-prev')) {
          el.value = el.getAttribute('data-prev');
        }
        el.removeAttribute('data-prev');
      });
    });
  });

  // === ЭФФЕКТ: main-qty-input очищается при фокусе, восстанавливается при blur ===
  const mainQtyInput = document.getElementById('main-qty-input');
  if (mainQtyInput) {
    mainQtyInput.removeAttribute('placeholder');
    mainQtyInput.addEventListener('focus', function (e) {
      const el = e.target;
      if (!el.hasAttribute('data-prev')) {
        el.setAttribute('data-prev', el.value);
      }
      el.value = '';
    });
    mainQtyInput.addEventListener('blur', function (e) {
      const el = e.target;
      if (el.value === '' && el.hasAttribute('data-prev')) {
        el.value = el.getAttribute('data-prev');
      }
      el.removeAttribute('data-prev');
    });
  }
</script>
