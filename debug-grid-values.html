<!DOCTYPE html>
<html>
<head>
    <title>Debug Grid Values</title>
</head>
<body>
    <h1>Debug Grid Values</h1>
    
    <h2>Текущие данные из page.json:</h2>
    <pre id="jsonData"></pre>
    
    <h2>Ожидаемое поведение:</h2>
    <ul>
        <li><strong>Основные настройки:</strong> 6 колонок, 1rem gap</li>
        <li><strong>Desktop настройки:</strong> 2 колонки, 1rem gap (установлено вручную)</li>
        <li><strong>Селектор должен показывать:</strong> 2 колонки</li>
    </ul>
    
    <script>
        // Загружаем данные блока
        fetch('/api/settings/get-page-data')
            .then(response => response.json())
            .then(data => {
                const block = data.pages
                    .find(page => page.id === 'contacts')
                    ?.blocks
                    ?.find(block => block.id === 'un2mtib3');
                
                if (block) {
                    document.getElementById('jsonData').textContent = JSON.stringify(block.displaySettings.gridSystem, null, 2);
                } else {
                    document.getElementById('jsonData').textContent = 'Блок не найден';
                }
            })
            .catch(error => {
                document.getElementById('jsonData').textContent = 'Ошибка: ' + error.message;
            });
    </script>
</body>
</html>
