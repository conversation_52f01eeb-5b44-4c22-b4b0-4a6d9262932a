---
import BlockRenderer from './BlockRenderer.astro';

export interface Props {
  blocks: Array<{
    id: string;
    type: string;
    enabled: boolean;
    order: number;
    content: {
      [lang: string]: any;
    };
    name?: string;
    displaySettings?: {
      maxWidth?: {
        value: number;
        unit: 'px' | '%';
      };
      alignment?: 'left' | 'center' | 'right';
      padding?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
        unit: 'px' | '%';
      };
      margin?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
        unit: 'px' | '%';
      };
      gridColumns?: number;
      elementGap?: {
        value: number;
        unit: 'px' | 'rem';
      };
      gridSystem?: {
        enabled: boolean;
        mode: 'simple';
        // Основные настройки Grid
        columns: number;
        gap: { value: number; unit: string };
        itemAlignment: string;
        contentAlignment: string;
        // Дополнительные настройки
        autoRows?: string;
        justifyItems?: string;
        alignContent?: string;
        justifyContent?: string;
        // Адаптивные настройки
        responsive?: {
          desktop: {
            columns: number;
            gap?: { value: number; unit: string };
          };
          tablet: {
            columns: number;
            gap?: { value: number; unit: string };
          };
          mobile: {
            columns: number;
            gap?: { value: number; unit: string };
          };
        };
        // Элементы Grid
        items?: any[];
        previewActive?: boolean;
      };
    };
  }>;
  lang?: string;
  containerClass?: string;
}

const { blocks = [], lang = 'ru', containerClass = '' } = Astro.props;

// Фильтруем и сортируем блоки
const enabledBlocks = blocks
  .filter(block => block.enabled)
  .sort((a, b) => (a.order || 0) - (b.order || 0));

/**
 * Получает CSS классы для контейнера блока в зависимости от типа
 */
function getBlockContainerClass(blockType) {
  const baseClasses = 'block-content mb-8';

  switch (blockType) {
    case 'hero':
      return `${baseClasses} hero-block`;
    case 'text':
      return `${baseClasses} text-block prose prose-lg max-w-none`;
    case 'gallery':
      return `${baseClasses} gallery-block`;
    case 'video':
      return `${baseClasses} video-block`;
    case 'contacts':
      return `${baseClasses} contacts-block`;
    case 'map':
      return `${baseClasses} map-block`;
    case 'slider':
      return `${baseClasses} slider-block`;
    case 'custom':
      return `${baseClasses} custom-block prose prose-lg max-w-none`;
    default:
      return `${baseClasses} default-block prose prose-lg max-w-none`;
  }
}

/**
 * Генерирует inline CSS стили на основе настроек отображения блока
 */
function generateBlockStyles(displaySettings) {
  if (!displaySettings) return '';

  const styles = [];

  // Максимальная ширина
  if (displaySettings.maxWidth) {
    styles.push(`max-width: ${displaySettings.maxWidth.value}${displaySettings.maxWidth.unit}`);
  }

  // Выравнивание
  if (displaySettings.alignment) {
    switch (displaySettings.alignment) {
      case 'center':
        styles.push('margin-left: auto', 'margin-right: auto');
        break;
      case 'right':
        styles.push('margin-left: auto', 'margin-right: 0');
        break;
      case 'left':
      default:
        styles.push('margin-left: 0', 'margin-right: auto');
        break;
    }
  }

  // Внутренние отступы (padding)
  if (displaySettings.padding) {
    const { top, right, bottom, left, unit } = displaySettings.padding;
    styles.push(`padding: ${top}${unit} ${right}${unit} ${bottom}${unit} ${left}${unit}`);
  }

  // Внешние отступы (margin) - дополнительные к выравниванию
  if (displaySettings.margin) {
    const { top, right, bottom, left, unit } = displaySettings.margin;
    // Применяем только top и bottom, left и right управляются выравниванием
    styles.push(`margin-top: ${top}${unit}`, `margin-bottom: ${bottom}${unit}`);

    // Если выравнивание не задано, применяем left и right margin
    if (!displaySettings.alignment) {
      styles.push(`margin-left: ${left}${unit}`, `margin-right: ${right}${unit}`);
    }
  }

  return styles.join('; ');
}

/**
 * Генерирует CSS стили для Grid-системы
 */
function generateGridStyles(gridSystem) {
  if (!gridSystem || !gridSystem.enabled) return '';

  const styles = [];
  const { columns, gap, itemAlignment, contentAlignment, autoRows, justifyItems, alignContent, justifyContent } = gridSystem;

  styles.push('display: grid');
  styles.push(`grid-template-columns: repeat(${columns || 2}, 1fr)`);

  if (gap) {
    styles.push(`gap: ${gap.value}${gap.unit}`);
  }

  if (itemAlignment) {
    styles.push(`align-items: ${itemAlignment}`);
  }

  if (contentAlignment) {
    styles.push(`justify-content: ${contentAlignment}`);
  }

  if (autoRows) {
    styles.push(`grid-auto-rows: ${autoRows}`);
  }

  if (justifyItems) {
    styles.push(`justify-items: ${justifyItems}`);
  }

  if (alignContent) {
    styles.push(`align-content: ${alignContent}`);
  }

  if (justifyContent) {
    styles.push(`justify-content: ${justifyContent}`);
  }

  return styles.join('; ');
}

/**
 * Проверяет, есть ли контент в блоке
 */
function hasBlockContent(block, lang) {
  try {
    const content = block?.content?.[lang];
    if (!content) return false;

    // Если это JSON строка, проверяем что она не пустая
    if (typeof content === 'string') {
      if (content.trim() === '') return false;
      try {
        const parsed = JSON.parse(content);
        return parsed.blocks && parsed.blocks.length > 0;
      } catch {
        return content.trim() !== '';
      }
    }

    // Если это объект, проверяем что есть данные
    if (typeof content === 'object') {
      return Object.keys(content).length > 0;
    }

    return false;
  } catch (error) {
    console.error(`Ошибка проверки контента блока ${block.id}:`, error);
    return false;
  }
}
---

<div class={`content-renderer ${containerClass}`}>
  {enabledBlocks.map(block => {
    // Пропускаем блоки без контента
    if (!hasBlockContent(block, lang)) {
      return null;
    }

    // Генерируем стили для блока
    const blockStyles = generateBlockStyles(block.displaySettings);

    // Проверяем, включена ли новая Grid-система
    const hasGridSystem = block.displaySettings?.gridSystem?.enabled;

    if (hasGridSystem) {
      // Если Grid-система включена, рендерим блок с Grid-контейнером
      // Grid-стили будут применены через CSS к .editorjs-grid-container

      return (
        <section
          class={`${getBlockContainerClass(block.type)} grid-enabled-block`}
          style={blockStyles}
          data-block-id={block.id}
          data-block-type={block.type}
          data-block-order={block.order}
          data-grid-enabled="true"
          data-grid-mode={block.displaySettings?.gridSystem?.mode || 'simple'}
          data-grid-columns={block.displaySettings?.gridSystem?.columns || 2}
          data-grid-gap={`${block.displaySettings?.gridSystem?.gap?.value || 1}${block.displaySettings?.gridSystem?.gap?.unit || 'rem'}`}
        >
          {/* Рендерим блок через BlockRenderer с Grid-поддержкой */}
          <BlockRenderer block={block} lang={lang} gridEnabled={true} gridSettings={block.displaySettings?.gridSystem} />
        </section>
      );
    } else {
      // Обычный рендеринг без Grid-системы
      return (
        <section
          class={`${getBlockContainerClass(block.type)}`}
          style={blockStyles}
          data-block-id={block.id}
          data-block-type={block.type}
          data-block-order={block.order}
          data-grid-enabled="false"
        >
          {/* Рендерим блок через BlockRenderer */}
          <BlockRenderer block={block} lang={lang} gridEnabled={false} />
        </section>
      );
    }
  })}

  {/* Показываем сообщение если нет активных блоков */}
  {enabledBlocks.length === 0 && (
    <div class="no-content-message text-center py-12">
      <p class="text-gray-500 text-lg">Контент для отображения отсутствует</p>
      {import.meta.env.DEV && (
        <p class="text-gray-400 text-sm mt-2">
          Добавьте блоки контента в админ-панели или проверьте настройки языка
        </p>
      )}
    </div>
  )}
</div>

<style>
  /* Стили для блоков контента */
  .content-renderer {
    @apply w-full;
  }

  .block-content {
    @apply relative;
  }

  /* Специальные стили для разных типов блоков */
  .hero-block {
    @apply mb-0; /* Hero блоки обычно без отступа снизу */
  }

  .text-block {
    @apply max-w-4xl mx-auto; /* Ограничиваем ширину текстовых блоков */
  }

  .gallery-block {
    @apply w-full; /* Галереи на всю ширину */
  }

  .video-block {
    @apply max-w-4xl mx-auto; /* Видео с ограниченной шириной */
  }

  .contacts-block {
    @apply w-full; /* Контакты на всю ширину */
  }

  .map-block {
    @apply w-full; /* Карты на всю ширину */
  }

  .slider-block {
    @apply w-full mb-0; /* Слайдеры на всю ширину без отступа */
  }

  .custom-block {
    @apply max-w-4xl mx-auto; /* Кастомные блоки с ограниченной шириной */
  }

  /* Отладочная информация только в режиме разработки */
  .block-debug-info {
    font-family: 'Courier New', monospace;
  }

  /* Стили для контента из Editor.js */
  .block-content :global(h1),
  .block-content :global(h2),
  .block-content :global(h3),
  .block-content :global(h4),
  .block-content :global(h5),
  .block-content :global(h6) {
    @apply font-bold text-gray-900 leading-tight;
  }

  .block-content :global(p) {
    @apply text-gray-700 leading-relaxed;
  }

  .block-content :global(a) {
    @apply text-blue-600 hover:text-blue-800 underline;
  }

  .block-content :global(img) {
    @apply max-w-full h-auto;
  }

  .block-content :global(blockquote) {
    @apply border-l-4 border-blue-500 pl-6 italic;
  }

  .block-content :global(code) {
    @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
  }

  .block-content :global(pre) {
    @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;
  }

  .block-content :global(pre code) {
    @apply bg-transparent p-0;
  }

  .block-content :global(ul),
  .block-content :global(ol) {
    @apply space-y-2;
  }

  .block-content :global(li) {
    @apply text-gray-700;
  }

  .block-content :global(table) {
    @apply w-full border-collapse;
  }

  .block-content :global(th),
  .block-content :global(td) {
    @apply border border-gray-300 px-4 py-2;
  }

  .block-content :global(th) {
    @apply bg-gray-100 font-semibold;
  }

  /* Новая Grid-система будет обрабатываться отдельными компонентами */

  /* Grid колонки для элементов EditorJS */
  .block-with-grid.grid-cols-1 .block-renderer,
  .block-content.block-with-grid.grid-cols-1 .block-renderer {
    grid-template-columns: 1fr !important;
  }

  .block-with-grid.grid-cols-2 .block-renderer,
  .block-content.block-with-grid.grid-cols-2 .block-renderer {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .block-with-grid.grid-cols-3 .block-renderer,
  .block-content.block-with-grid.grid-cols-3 .block-renderer {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .block-with-grid.grid-cols-4 .block-renderer,
  .block-content.block-with-grid.grid-cols-4 .block-renderer {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  .block-with-grid.grid-cols-5 .block-renderer {
    grid-template-columns: repeat(5, 1fr);
  }

  .block-with-grid.grid-cols-6 .block-renderer {
    grid-template-columns: repeat(6, 1fr);
  }

  .block-with-grid.grid-cols-7 .block-renderer {
    grid-template-columns: repeat(7, 1fr);
  }

  .block-with-grid.grid-cols-8 .block-renderer {
    grid-template-columns: repeat(8, 1fr);
  }

  .block-with-grid.grid-cols-9 .block-renderer {
    grid-template-columns: repeat(9, 1fr);
  }

  .block-with-grid.grid-cols-10 .block-renderer {
    grid-template-columns: repeat(10, 1fr);
  }

  .block-with-grid.grid-cols-11 .block-renderer {
    grid-template-columns: repeat(11, 1fr);
  }

  .block-with-grid.grid-cols-12 .block-renderer {
    grid-template-columns: repeat(12, 1fr);
  }

  /* Старые стили grid удалены - используется новая система */

  /* Убираем отступы у элементов внутри grid-item */
  .block-with-grid .block-renderer :global(.grid-item > *:first-child) {
    margin-top: 0;
  }

  .block-with-grid .block-renderer :global(.grid-item > *:last-child) {
    margin-bottom: 0;
  }

  /* Адаптивные стили для изображений в grid */
  .block-with-grid .block-renderer :global(figure) {
    width: 100%;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .block-with-grid .block-renderer :global(img) {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
  }

  .block-with-grid .block-renderer :global(img:hover) {
    transform: scale(1.02);
  }

  .block-with-grid .block-renderer :global(figcaption) {
    margin-top: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* Адаптивные стили для текста в grid */
  .block-with-grid .block-renderer :global(p) {
    margin-bottom: 0.5rem;
    word-wrap: break-word;
    line-height: 1.6;
    text-align: left;
  }

  .block-with-grid .block-renderer :global(h1),
  .block-with-grid .block-renderer :global(h2),
  .block-with-grid .block-renderer :global(h3),
  .block-with-grid .block-renderer :global(h4),
  .block-with-grid .block-renderer :global(h5),
  .block-with-grid .block-renderer :global(h6) {
    margin-bottom: 0.75rem;
    word-wrap: break-word;
    line-height: 1.3;
    font-weight: 600;
  }

  .block-with-grid .block-renderer :global(h1) { font-size: 1.5rem; }
  .block-with-grid .block-renderer :global(h2) { font-size: 1.25rem; }
  .block-with-grid .block-renderer :global(h3) { font-size: 1.125rem; }
  .block-with-grid .block-renderer :global(h4) { font-size: 1rem; }
  .block-with-grid .block-renderer :global(h5) { font-size: 0.875rem; }
  .block-with-grid .block-renderer :global(h6) { font-size: 0.75rem; }

  /* Стили для списков в grid */
  .block-with-grid .block-renderer :global(ul),
  .block-with-grid .block-renderer :global(ol) {
    margin-bottom: 0.5rem;
    padding-left: 1.25rem;
  }

  .block-with-grid .block-renderer :global(li) {
    margin-bottom: 0.25rem;
    line-height: 1.5;
  }

  /* Стили для цитат в grid */
  .block-with-grid .block-renderer :global(blockquote) {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 0.5rem 0;
    font-style: italic;
    color: #4b5563;
    background-color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
  }

  /* Стили для кода в grid */
  .block-with-grid .block-renderer :global(code) {
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
  }

  .block-with-grid .block-renderer :global(pre) {
    background-color: #1f2937;
    color: #10b981;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
  }

  .block-with-grid .block-renderer :global(pre code) {
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  /* Стили для таблиц в grid */
  .block-with-grid .block-renderer :global(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5rem 0;
    font-size: 0.875rem;
  }

  .block-with-grid .block-renderer :global(th),
  .block-with-grid .block-renderer :global(td) {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: left;
  }

  .block-with-grid .block-renderer :global(th) {
    background-color: #f3f4f6;
    font-weight: 600;
  }

  /* Стили для ссылок в grid */
  .block-with-grid .block-renderer :global(a) {
    color: #3b82f6;
    text-decoration: underline;
    transition: color 0.2s ease-in-out;
  }

  .block-with-grid .block-renderer :global(a:hover) {
    color: #1d4ed8;
  }

  /* Стили для разделителей в grid */
  .block-with-grid .block-renderer :global(hr) {
    border: none;
    height: 2px;
    background-color: #e5e7eb;
    margin: 1rem 0;
    border-radius: 1px;
  }

  /* Адаптивность для мобильных устройств */
  @media (max-width: 768px) {
    .text-block,
    .video-block,
    .custom-block {
      @apply max-w-none mx-4;
    }

    .block-content :global(iframe) {
      @apply w-full h-auto;
    }

    .block-content :global(table) {
      @apply text-sm;
    }

    .block-content :global(th),
    .block-content :global(td) {
      @apply px-2 py-1;
    }

    /* На мобильных устройствах grid-колонки адаптируются */
    .block-content[data-block-grid-columns="3"],
    .block-content[data-block-grid-columns="4"] {
      grid-template-columns: repeat(2, 1fr);
    }

    .block-content[data-block-grid-columns="5"],
    .block-content[data-block-grid-columns="6"] {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 480px) {
    /* На очень маленьких экранах все grid-блоки становятся одноколоночными */
    .block-content[data-block-grid-columns] {
      grid-template-columns: 1fr !important;
    }
  }

  /* ===== СТИЛИ ДЛЯ НОВОЙ GRID-СИСТЕМЫ ===== */

  /* Grid-контейнер для блоков с включенной Grid-системой */
  .grid-enabled-block {
    @apply relative;
  }

  /* Grid-контейнер для EditorJS элементов - базовые стили */
  .grid-enabled-block :global(.editorjs-grid-container) {
    display: grid !important;
    gap: 1rem; /* Базовый отступ по умолчанию */
    width: 100%;
  }

  /* Базовые стили для Grid-контейнера */
  .grid-enabled-block :global(.editorjs-grid-container[data-grid-mode="simple"]) {
    display: grid !important;
  }

  /* Стили для разного количества колонок */
  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="1"]) {
    grid-template-columns: 1fr;
  }

  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="2"]) {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="3"]) {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="4"]) {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="5"]) {
    grid-template-columns: repeat(5, 1fr);
  }

  .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="6"]) {
    grid-template-columns: repeat(6, 1fr);
  }

  /* Применяем gap из data-атрибута */
  .grid-enabled-block :global(.editorjs-grid-container[data-grid-gap]) {
    gap: var(--grid-gap, 1rem);
  }

  /* Каждый элемент EditorJS становится Grid-элементом */
  .grid-enabled-block :global(.ce-block) {
    @apply w-full;
    min-height: 0; /* Позволяет элементам сжиматься в Grid */
  }

  /* Убираем стандартные отступы EditorJS в Grid-режиме */
  .grid-enabled-block :global(.ce-block:not(:last-child)) {
    margin-bottom: 0 !important;
  }

  /* Убираем отступы у всех элементов в Grid */
  .grid-enabled-block :global(.editorjs-grid-container .ce-block) {
    margin: 0 !important;
  }

  /* Стили для элементов в Grid */
  .grid-enabled-block :global(.ce-paragraph),
  .grid-enabled-block :global(.ce-header),
  .grid-enabled-block :global(.ce-list),
  .grid-enabled-block :global(.ce-image) {
    @apply h-full flex flex-col justify-center;
  }

  /* ===== АДАПТИВНЫЕ СТИЛИ ===== */

  /* Large Desktop (1280px и больше) - используем основные настройки */

  /* Desktop (1025px - 1280px) */
  @media screen and (min-width: 1025px) and (max-width: 1280px) {
    /* Используем desktop настройки, если они заданы */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="1"]) {
      grid-template-columns: 1fr !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="2"]) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="3"]) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="4"]) {
      grid-template-columns: repeat(4, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="5"]) {
      grid-template-columns: repeat(5, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-columns="6"]) {
      grid-template-columns: repeat(6, 1fr) !important;
    }

    /* Применяем desktop gap */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-desktop-gap]) {
      gap: var(--grid-desktop-gap) !important;
    }

    /* Fallback: если desktop настройки не заданы, используем основные настройки */
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="1"]) {
      grid-template-columns: 1fr !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="2"]) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="3"]) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="4"]) {
      grid-template-columns: repeat(4, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="5"]) {
      grid-template-columns: repeat(5, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-columns])[data-grid-columns="6"]) {
      grid-template-columns: repeat(6, 1fr) !important;
    }

    /* Fallback для gap - если desktop gap не задан, используем основной */
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-desktop-gap])[data-grid-gap]) {
      gap: var(--grid-gap) !important;
    }
  }

  /* Tablet (769px - 1024px) */
  @media screen and (min-width: 769px) and (max-width: 1024px) {
    /* Используем tablet настройки, если они заданы */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-tablet-columns="1"]) {
      grid-template-columns: 1fr !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-tablet-columns="2"]) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-tablet-columns="3"]) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-tablet-columns="4"]) {
      grid-template-columns: repeat(4, 1fr) !important;
    }

    /* Если tablet настройки не заданы, используем fallback */
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-tablet-columns])[data-grid-columns="3"]),
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-tablet-columns])[data-grid-columns="4"]),
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-tablet-columns])[data-grid-columns="5"]),
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-tablet-columns])[data-grid-columns="6"]) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    /* Применяем tablet gap */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-tablet-gap]) {
      gap: var(--grid-tablet-gap) !important;
    }
  }

  /* Mobile (≤768px) */
  @media screen and (max-width: 768px) {
    /* Используем mobile настройки, если они заданы */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-mobile-columns="1"]) {
      grid-template-columns: 1fr !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-mobile-columns="2"]) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .grid-enabled-block :global(.editorjs-grid-container[data-grid-mobile-columns="3"]) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    /* Применяем mobile gap */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-mobile-gap]) {
      gap: var(--grid-mobile-gap) !important;
    }

    /* ВАЖНО: Fallback для всех Grid-контейнеров без mobile настроек */
    .grid-enabled-block :global(.editorjs-grid-container:not([data-grid-mobile-columns])) {
      grid-template-columns: 1fr !important;
    }

    /* Дополнительный fallback для многоколоночных Grid */
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="2"]:not([data-grid-mobile-columns])),
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="3"]:not([data-grid-mobile-columns])),
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="4"]:not([data-grid-mobile-columns])),
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="5"]:not([data-grid-mobile-columns])),
    .grid-enabled-block :global(.editorjs-grid-container[data-grid-columns="6"]:not([data-grid-mobile-columns])) {
      grid-template-columns: 1fr !important;
    }
  }
</style>

<script>
  // Применяем CSS переменные из data-атрибутов для Grid-контейнеров
  document.addEventListener('DOMContentLoaded', function() {
    const gridContainers = document.querySelectorAll('.editorjs-grid-container[data-grid-gap]');

    gridContainers.forEach(container => {
      const htmlContainer = container as HTMLElement;

      // Основные стили
      const gap = htmlContainer.getAttribute('data-grid-gap');
      if (gap) {
        htmlContainer.style.setProperty('--grid-gap', gap);
        htmlContainer.style.gap = gap;
      }

      const alignItems = htmlContainer.getAttribute('data-grid-align-items');
      if (alignItems) {
        htmlContainer.style.alignItems = alignItems;
      }

      // Применяем contentAlignment (старое поле)
      const contentAlignment = htmlContainer.getAttribute('data-grid-content-alignment');
      if (contentAlignment) {
        htmlContainer.style.justifyContent = contentAlignment;
      }

      // Применяем justifyContent (новое поле, приоритетнее)
      const justifyContent = htmlContainer.getAttribute('data-grid-justify-content');
      if (justifyContent) {
        htmlContainer.style.justifyContent = justifyContent;
      }

      const justifyItems = htmlContainer.getAttribute('data-grid-justify-items');
      if (justifyItems) {
        htmlContainer.style.justifyItems = justifyItems;
      }

      const alignContent = htmlContainer.getAttribute('data-grid-align-content');
      if (alignContent) {
        htmlContainer.style.alignContent = alignContent;
      }

      const autoRows = htmlContainer.getAttribute('data-grid-auto-rows');
      if (autoRows) {
        htmlContainer.style.gridAutoRows = autoRows;
      }

      // Адаптивные стили через CSS переменные
      const desktopGap = htmlContainer.getAttribute('data-grid-desktop-gap');
      if (desktopGap) {
        htmlContainer.style.setProperty('--grid-desktop-gap', desktopGap);
      }

      const tabletGap = htmlContainer.getAttribute('data-grid-tablet-gap');
      if (tabletGap) {
        htmlContainer.style.setProperty('--grid-tablet-gap', tabletGap);
      }

      const mobileGap = htmlContainer.getAttribute('data-grid-mobile-gap');
      if (mobileGap) {
        htmlContainer.style.setProperty('--grid-mobile-gap', mobileGap);
      }
    });
  });
</script>
