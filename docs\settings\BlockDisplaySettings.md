# Настройки отображения блоков

Система настроек страниц теперь поддерживает расширенные настройки отображения для каждого блока контента. Эти настройки позволяют точно контролировать внешний вид и расположение блоков на странице.

## Возможности

### 1. Максимальная ширина блока

Позволяет ограничить максимальную ширину блока на странице:

- **Значение**: от 1 до 100
- **Единицы**: px (пиксели) или % (проценты)
- **По умолчанию**: 100%

### 2. Выравнивание контента

Определяет, как блок будет расположен на странице:

- **left** - выравнивание по левому краю
- **center** - выравнивание по центру
- **right** - выравнивание по правому краю
- **По умолчанию**: left

### 3. Внутренние отступы (padding)

Контролируют пространство внутри блока между границами и контентом:

- **Стороны**: top, right, bottom, left
- **Диапазон**: от -1000 до 1000
- **Единицы**: px или %
- **По умолчанию**: 0px для всех сторон

### 4. Внешние отступы (margin)

Контролируют пространство вокруг блока:

- **Стороны**: top, right, bottom, left
- **Диапазон**: от -1000 до 1000 (отрицательные значения для наложения)
- **Единицы**: px или %
- **По умолчанию**: 0px для всех сторон
- **Примечание**: left и right margin переопределяются настройкой выравнивания

### 5. Grid-система

Определяет количество элементов контента, которые будут отображаться в одной строке:

- **Диапазон**: от 1 до 12 колонок
- **По умолчанию**: 1 (элементы идут друг за другом)
- **Адаптивность**: автоматически адаптируется под мобильные устройства

### 6. Отступы между элементами (Element Gap)

Контролирует расстояние между элементами в grid-сетке:

- **Диапазон**: от 0 до 100
- **Единицы**: px или rem
- **По умолчанию**: 16px
- **Применение**: только при количестве колонок > 1

## Структура данных

```typescript
interface BlockDisplaySettings {
  maxWidth?: {
    value: number;
    unit: 'px' | '%';
  };
  alignment?: 'left' | 'center' | 'right';
  padding?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  gridColumns?: number;
  elementGap?: {
    value: number;
    unit: 'px' | 'rem';
  };
}
```

## Пример использования

```json
{
  "id": "example-block",
  "type": "text",
  "enabled": true,
  "order": 1,
  "content": {
    "ru": "...",
    "en": "..."
  },
  "displaySettings": {
    "maxWidth": {
      "value": 80,
      "unit": "%"
    },
    "alignment": "center",
    "padding": {
      "top": 20,
      "right": 15,
      "bottom": 20,
      "left": 15,
      "unit": "px"
    },
    "margin": {
      "top": 0,
      "right": 0,
      "bottom": 30,
      "left": 0,
      "unit": "px"
    },
    "gridColumns": 2,
    "elementGap": {
      "value": 24,
      "unit": "px"
    }
  }
}
```

## Адаптивность

Система автоматически адаптирует настройки под разные размеры экранов:

### Планшеты (≤768px)
- Grid с 3-4 колонками становится 2-колоночным
- Grid с 5-6 колонками становится 2-колоночным

### Мобильные устройства (≤480px)
- Все grid-блоки становятся одноколоночными
- Максимальная ширина автоматически адаптируется

## Валидация

### Клиентская валидация
- Максимальная ширина: 1-100
- Grid колонки: 1-12
- Отступы: -1000 до 1000

### Серверная валидация
- Проверка типов данных
- Проверка допустимых значений
- Проверка единиц измерения

## Интеграция с EditorJS

Настройки отображения применяются к контенту блока, создавая адаптивные макеты. Это позволяет:

- Сохранить все возможности редактора
- Добавить визуальное оформление
- Создать адаптивные макеты с grid-системой
- Контролировать расположение элементов
- Автоматически адаптировать изображения, текст и другие элементы под grid-сетку

### Особенности Grid-системы

- **Автоматическая обертка**: каждый элемент EditorJS автоматически оборачивается в grid-item
- **Адаптивные стили**: изображения, текст и другие элементы автоматически адаптируются под размер grid-ячейки
- **Равномерное распределение**: элементы равномерно распределяются по grid-сетке
- **Hover-эффекты**: grid-элементы имеют интерактивные эффекты при наведении

## Использование в админ-панели

1. Перейдите к редактированию блока
2. Найдите секцию "Настройки отображения" над редактором
3. Настройте нужные параметры
4. Сохраните изменения

Настройки будут автоматически применены при отображении блока на фронтенде.
