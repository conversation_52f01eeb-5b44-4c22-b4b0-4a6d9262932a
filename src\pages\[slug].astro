---
import { loadPageSettings } from '../settings/utils/settingsLoader.js';
import PageLayout from '../layouts/PageLayout.astro';
import ContentRenderer from '../settings/components/content/ContentRenderer.astro';

const { slug } = Astro.params;
const settings = await loadPageSettings();
const pages = settings.pages || [];

// Найти страницу по slug (поиск по url.ru и url.en)
const page = pages.find(p => Object.values(p.url).includes('/' + slug));

if (!page) {
  return Astro.redirect('/404');
}

const lang = 'ru'; // TODO: определить язык пользователя/сайта
const seo = page.seo || {};
const breadcrumbs = [
  { text: 'Главная', url: '/' },
  { text: seo.title?.[lang] || page.id, url: '/' + slug }
];
---

<PageLayout title={seo.title?.[lang] || page.id} pageTitle={seo.title?.[lang] || page.id} breadcrumbs={breadcrumbs}>
  <div class="container mx-auto px-4 py-8">
    <ContentRenderer
      blocks={page.blocks || []}
      lang={lang}
      containerClass="page-content"
    />
  </div>
</PageLayout>