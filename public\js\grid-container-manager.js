/**
 * Менеджер Grid Container для управления контейнерами и их элементами
 */
class GridContainerManager {
  constructor() {
    this.containers = new Map();
    this.activeContainerId = null;
    this.editorInstance = null;
    this.init();
  }

  init() {
    this.bindEvents();
    console.log('🔧 Grid Container Manager инициализирован');
  }

  bindEvents() {
    // Слушаем события активации контейнеров
    document.addEventListener('gridContainerActivated', (e) => {
      this.handleContainerActivation(e.detail);
    });

    // Слушаем изменения в grid-системе
    document.addEventListener('change', (e) => {
      if (e.target.name === 'gridEnabled') {
        this.handleGridToggle(e.target.checked);
      }
    });

    // Слушаем добавление новых блоков в EditorJS
    document.addEventListener('editorBlockAdded', (e) => {
      this.handleNewBlock(e.detail);
    });
  }

  // Регистрация экземпляра EditorJS
  registerEditor(editorInstance) {
    this.editorInstance = editorInstance;
    console.log('📝 EditorJS зарегистрирован в Grid Container Manager');
  }

  // Обработка активации контейнера
  handleContainerActivation(detail) {
    const { containerId, container } = detail;

    // Деактивируем предыдущий активный контейнер
    if (this.activeContainerId && this.activeContainerId !== containerId) {
      const prevContainer = this.containers.get(this.activeContainerId);
      if (prevContainer) {
        prevContainer.deactivate();
      }
    }

    // Устанавливаем новый активный контейнер
    this.activeContainerId = containerId;
    this.containers.set(containerId, container);

    console.log(`📦 Активирован контейнер: ${containerId}`);

    // Уведомляем grid-систему об изменении
    this.notifyGridSystemUpdate();
  }

  // Обработка переключения grid-системы
  handleGridToggle(enabled) {
    if (!enabled) {
      // Если grid отключен, деактивируем все контейнеры
      this.deactivateAllContainers();
    } else {
      // Если grid включен, автоматически переключаемся в режим контейнеров
      console.log('🔄 Grid включен, активирован режим контейнеров');
    }

    // Обновляем доступность Grid Container инструмента
    this.updateGridContainerAvailability(enabled);
  }

  // Обработка добавления нового блока
  handleNewBlock(detail) {
    const { blockData, blockIndex } = detail;

    // Если есть активный контейнер, добавляем блок в него
    if (this.activeContainerId) {
      const activeContainer = this.containers.get(this.activeContainerId);
      if (activeContainer && blockData.type !== 'gridContainer') {
        this.addBlockToContainer(activeContainer, blockData);
      }
    }
  }

  // Добавление блока в контейнер
  addBlockToContainer(container, blockData) {
    const itemData = {
      id: blockData.id || this.generateId(),
      type: blockData.type,
      data: blockData.data,
      containerId: container.data.id
    };

    container.addItem(itemData);
    console.log(`➕ Блок ${blockData.type} добавлен в контейнер ${container.data.id}`);
  }

  // Создание нового контейнера
  createContainer() {
    if (!this.isGridEnabled()) {
      console.warn('⚠️ Grid-система не включена, нельзя создать контейнер');
      return null;
    }

    // Деактивируем текущий активный контейнер
    this.deactivateCurrentContainer();

    // Создаем новый контейнер через EditorJS
    if (this.editorInstance) {
      const containerData = {
        id: this.generateId(),
        items: [],
        isActive: true,
        settings: {}
      };

      // Добавляем блок Grid Container в EditorJS
      this.editorInstance.blocks.insert('gridContainer', containerData);

      console.log(`🆕 Создан новый контейнер: ${containerData.id}`);
      return containerData.id;
    }

    return null;
  }

  // Деактивация текущего активного контейнера
  deactivateCurrentContainer() {
    if (this.activeContainerId) {
      const container = this.containers.get(this.activeContainerId);
      if (container) {
        container.deactivate();
      }
      this.activeContainerId = null;
    }
  }

  // Деактивация всех контейнеров
  deactivateAllContainers() {
    this.containers.forEach(container => {
      container.deactivate();
    });
    this.activeContainerId = null;
  }

  // Проверка включенной grid-системы
  isGridEnabled() {
    const gridToggle = document.querySelector('input[name="gridEnabled"]:checked');
    return gridToggle !== null;
  }

  // Обновление доступности Grid Container инструмента
  updateGridContainerAvailability(enabled) {
    // Эта функция будет вызываться при изменении состояния grid-системы
    // EditorJS автоматически обновит доступные инструменты
    if (this.editorInstance && this.editorInstance.configuration) {
      console.log(`🔄 Grid Container ${enabled ? 'доступен' : 'недоступен'}`);
    }
  }

  // Уведомление grid-системы об обновлении
  notifyGridSystemUpdate() {
    const event = new CustomEvent('gridContainerUpdate', {
      detail: {
        activeContainerId: this.activeContainerId,
        containers: Array.from(this.containers.keys())
      }
    });
    document.dispatchEvent(event);
  }

  // Получение данных всех контейнеров
  getContainersData() {
    const containersData = [];

    this.containers.forEach((container, id) => {
      containersData.push({
        id: id,
        isActive: container.data.isActive,
        items: container.data.items,
        settings: container.data.settings
      });
    });

    return containersData;
  }

  // Получение активного контейнера
  getActiveContainer() {
    if (this.activeContainerId) {
      return this.containers.get(this.activeContainerId);
    }
    return null;
  }

  // Удаление контейнера
  removeContainer(containerId) {
    if (this.containers.has(containerId)) {
      this.containers.delete(containerId);

      if (this.activeContainerId === containerId) {
        this.activeContainerId = null;
      }

      console.log(`🗑️ Контейнер ${containerId} удален`);
      this.notifyGridSystemUpdate();
    }
  }

  // Генерация уникального ID
  generateId() {
    return 'container_' + Math.random().toString(36).substr(2, 9);
  }

  // Получение настроек для сохранения
  getSettings() {
    return {
      mode: 'container', // Всегда режим контейнеров
      activeContainerId: this.activeContainerId,
      containers: this.getContainersData()
    };
  }

  // Загрузка настроек
  loadSettings(settings) {
    if (settings.mode === 'container' && settings.containers) {
      // Восстанавливаем контейнеры
      settings.containers.forEach(containerData => {
        // Контейнеры будут восстановлены при загрузке EditorJS
        console.log(`📥 Загружен контейнер: ${containerData.id}`);
      });

      if (settings.activeContainerId) {
        this.activeContainerId = settings.activeContainerId;
      }
    }
  }

  // Экспорт данных для сохранения в grid-системе
  exportForGridSystem() {
    const containers = this.getContainersData();

    return {
      enabled: this.isGridEnabled(),
      mode: 'container', // Всегда режим контейнеров
      containers: containers,
      activeContainerId: this.activeContainerId,
      previewActive: true
    };
  }
}

// Создаем глобальный экземпляр менеджера
window.GridContainerManager = new GridContainerManager();

// Экспортируем для использования в модулях
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GridContainerManager;
}
