import { loadPageSettings } from './settingsLoader.js';
import { savePageSettings } from './settingsSaver.js';

/**
 * Утилита для очистки дублирующих страниц
 * Удаляет страницы без ID или с дублирующими ID
 */
export async function cleanupDuplicatePages() {
  try {
    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    
    console.log(`Исходное количество страниц: ${pages.length}`);
    
    // Удаляем страницы без ID
    const pagesWithId = pages.filter(page => page.id && page.id.trim() !== '');
    console.log(`Страниц без ID удалено: ${pages.length - pagesWithId.length}`);
    
    // Удаляем дублирующие страницы (оставляем первую встреченную)
    const uniquePages = [];
    const seenIds = new Set();
    
    for (const page of pagesWithId) {
      if (!seenIds.has(page.id)) {
        seenIds.add(page.id);
        uniquePages.push(page);
      } else {
        console.log(`Удален дубликат страницы с ID: ${page.id}`);
      }
    }
    
    console.log(`Итоговое количество страниц: ${uniquePages.length}`);
    
    // Сохраняем очищенные данные
    settings.pages = uniquePages;
    await savePageSettings(settings);
    
    return {
      success: true,
      originalCount: pages.length,
      finalCount: uniquePages.length,
      removedCount: pages.length - uniquePages.length
    };
    
  } catch (error) {
    console.error('Ошибка при очистке страниц:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Проверяет наличие дублирующих страниц
 */
export async function checkForDuplicates() {
  try {
    const settings = await loadPageSettings();
    const pages = settings.pages || [];
    
    const duplicates = [];
    const seenIds = new Set();
    const pagesWithoutId = [];
    
    for (const page of pages) {
      if (!page.id || page.id.trim() === '') {
        pagesWithoutId.push(page);
      } else if (seenIds.has(page.id)) {
        duplicates.push(page.id);
      } else {
        seenIds.add(page.id);
      }
    }
    
    return {
      hasDuplicates: duplicates.length > 0 || pagesWithoutId.length > 0,
      duplicateIds: duplicates,
      pagesWithoutId: pagesWithoutId.length,
      totalPages: pages.length,
      uniquePages: seenIds.size
    };
    
  } catch (error) {
    console.error('Ошибка при проверке дубликатов:', error);
    return {
      error: error.message
    };
  }
}
