/**
 * Утилита для парсинга элементов EditorJS для Grid-системы
 */
class EditorJSGridParser {
  
  /**
   * Преобразует блоки EditorJS в элементы Grid
   * @param {Array} editorBlocks - Блоки из EditorJS
   * @returns {Array} Массив элементов для Grid
   */
  static parseBlocksForGrid(editorBlocks) {
    if (!Array.isArray(editorBlocks)) {
      return [];
    }

    return editorBlocks.map((block, index) => ({
      id: block.id || `block-${index}`,
      type: block.type || 'paragraph',
      order: index + 1,
      editorData: block.data || {},
      gridColumn: undefined,
      gridRow: undefined
    }));
  }

  /**
   * Рендерит блок EditorJS в HTML для Grid-превью
   * @param {Object} gridItem - Элемент Grid с данными EditorJS
   * @returns {string} HTML строка
   */
  static renderGridItem(gridItem) {
    const { type, editorData } = gridItem;

    switch (type) {
      case 'paragraph':
        return this.renderParagraph(editorData);
      
      case 'header':
        return this.renderHeader(editorData);
      
      case 'list':
        return this.renderList(editorData);
      
      case 'quote':
        return this.renderQuote(editorData);
      
      case 'code':
        return this.renderCode(editorData);
      
      case 'delimiter':
        return this.renderDelimiter(editorData);
      
      case 'table':
        return this.renderTable(editorData);
      
      case 'image':
        return this.renderImage(editorData);
      
      case 'embed':
        return this.renderEmbed(editorData);
      
      case 'linkTool':
        return this.renderLinkTool(editorData);
      
      default:
        return this.renderDefault(editorData, type);
    }
  }

  /**
   * Рендерит параграф
   */
  static renderParagraph(data) {
    const text = data.text || '';
    return `<div class="grid-item-content paragraph">
      <p class="text-sm text-gray-700 leading-relaxed">${text}</p>
    </div>`;
  }

  /**
   * Рендерит заголовок
   */
  static renderHeader(data) {
    const text = data.text || '';
    const level = data.level || 1;
    const sizes = {
      1: 'text-lg font-bold',
      2: 'text-base font-bold',
      3: 'text-sm font-bold',
      4: 'text-sm font-semibold',
      5: 'text-xs font-semibold',
      6: 'text-xs font-medium'
    };
    const className = sizes[level] || sizes[1];
    
    return `<div class="grid-item-content header">
      <h${level} class="${className} text-gray-900 mb-1">${text}</h${level}>
    </div>`;
  }

  /**
   * Рендерит список
   */
  static renderList(data) {
    const style = data.style || 'unordered';
    const items = data.items || [];
    
    if (items.length === 0) {
      return `<div class="grid-item-content list">
        <p class="text-xs text-gray-500">Пустой список</p>
      </div>`;
    }

    const listItems = items.map(item => {
      const content = typeof item === 'string' ? item : (item.content || '');
      return `<li class="text-xs text-gray-700">${content}</li>`;
    }).join('');

    const tag = style === 'ordered' ? 'ol' : 'ul';
    const className = style === 'ordered' ? 'list-decimal list-inside' : 'list-disc list-inside';

    return `<div class="grid-item-content list">
      <${tag} class="${className} space-y-1">${listItems}</${tag}>
    </div>`;
  }

  /**
   * Рендерит цитату
   */
  static renderQuote(data) {
    const text = data.text || '';
    const caption = data.caption || '';
    
    return `<div class="grid-item-content quote">
      <blockquote class="border-l-2 border-blue-500 pl-2 italic text-xs text-gray-700">
        ${text}
        ${caption ? `<cite class="block text-xs text-gray-500 mt-1">— ${caption}</cite>` : ''}
      </blockquote>
    </div>`;
  }

  /**
   * Рендерит код
   */
  static renderCode(data) {
    const code = data.code || '';
    
    return `<div class="grid-item-content code">
      <pre class="bg-gray-900 text-green-400 p-2 rounded text-xs overflow-hidden"><code>${this.escapeHtml(code)}</code></pre>
    </div>`;
  }

  /**
   * Рендерит разделитель
   */
  static renderDelimiter(data) {
    return `<div class="grid-item-content delimiter">
      <div class="flex justify-center items-center py-2">
        <div class="text-gray-400">* * *</div>
      </div>
    </div>`;
  }

  /**
   * Рендерит таблицу
   */
  static renderTable(data) {
    const content = data.content || [];
    
    if (content.length === 0) {
      return `<div class="grid-item-content table">
        <p class="text-xs text-gray-500">Пустая таблица</p>
      </div>`;
    }

    const rows = content.map(row => {
      const cells = row.map(cell => `<td class="border border-gray-300 px-1 py-1 text-xs">${cell}</td>`).join('');
      return `<tr>${cells}</tr>`;
    }).join('');

    return `<div class="grid-item-content table">
      <table class="w-full border-collapse text-xs">
        ${rows}
      </table>
    </div>`;
  }

  /**
   * Рендерит изображение
   */
  static renderImage(data) {
    const url = data.file?.url || data.url || '';
    const caption = data.caption || '';
    
    if (!url) {
      return `<div class="grid-item-content image">
        <div class="bg-gray-200 h-16 flex items-center justify-center rounded">
          <span class="text-xs text-gray-500">Изображение</span>
        </div>
      </div>`;
    }

    return `<div class="grid-item-content image">
      <img src="${url}" alt="${caption}" class="w-full h-16 object-cover rounded" />
      ${caption ? `<p class="text-xs text-gray-500 mt-1">${caption}</p>` : ''}
    </div>`;
  }

  /**
   * Рендерит встроенный контент
   */
  static renderEmbed(data) {
    const service = data.service || 'Unknown';
    const caption = data.caption || '';
    
    return `<div class="grid-item-content embed">
      <div class="bg-blue-100 border border-blue-300 rounded p-2 text-center">
        <div class="text-xs font-medium text-blue-800">${service}</div>
        ${caption ? `<div class="text-xs text-blue-600 mt-1">${caption}</div>` : ''}
      </div>
    </div>`;
  }

  /**
   * Рендерит ссылку
   */
  static renderLinkTool(data) {
    const link = data.link || '';
    const meta = data.meta || {};
    const title = meta.title || link;
    const description = meta.description || '';
    
    return `<div class="grid-item-content link">
      <div class="border border-gray-300 rounded p-2">
        <div class="text-xs font-medium text-blue-600 truncate">${title}</div>
        ${description ? `<div class="text-xs text-gray-500 mt-1 line-clamp-2">${description}</div>` : ''}
      </div>
    </div>`;
  }

  /**
   * Рендерит неизвестный тип блока
   */
  static renderDefault(data, type) {
    return `<div class="grid-item-content default">
      <div class="bg-gray-100 border border-gray-300 rounded p-2 text-center">
        <div class="text-xs font-medium text-gray-600">${type}</div>
        <div class="text-xs text-gray-500 mt-1">Неизвестный тип блока</div>
      </div>
    </div>`;
  }

  /**
   * Экранирует HTML символы
   */
  static escapeHtml(text) {
    if (typeof text !== 'string') return '';
    
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Получает краткое описание блока для отображения
   */
  static getBlockSummary(gridItem) {
    const { type, editorData } = gridItem;
    
    switch (type) {
      case 'paragraph':
        const text = editorData.text || '';
        return text.length > 30 ? text.substring(0, 30) + '...' : text;
      
      case 'header':
        return `H${editorData.level || 1}: ${editorData.text || ''}`;
      
      case 'list':
        const itemCount = editorData.items?.length || 0;
        return `Список (${itemCount} элементов)`;
      
      case 'quote':
        return `Цитата: ${editorData.text || ''}`;
      
      case 'code':
        return 'Блок кода';
      
      case 'table':
        const rowCount = editorData.content?.length || 0;
        return `Таблица (${rowCount} строк)`;
      
      case 'image':
        return 'Изображение';
      
      default:
        return type;
    }
  }
}

// Экспортируем для использования
window.EditorJSGridParser = EditorJSGridParser;
