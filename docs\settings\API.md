# Settings Module API Documentation

Документация API для системы настроек страниц с блочным редактированием.

## Структура API

```
src/pages/api/settings/
├── blocks.js           # API для управления блоками
├── pages.js            # API для управления страницами
├── save-block.js       # Сохранение блоков
└── save-page.js        # Сохранение страниц
```

## API Endpoints

### Блоки (`/api/settings/blocks`)

#### DELETE - Удаление блока
```javascript
fetch('/api/settings/blocks', {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ pageId: 'page-id', blockId: 'block-id' })
})
```

**Ответ:**
```json
{
  "success": true,
  "message": "Блок удален"
}
```

#### PUT - Перемещение блока
```javascript
fetch('/api/settings/blocks', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    pageId: 'page-id', 
    blockId: 'block-id', 
    direction: 'up' // или 'down'
  })
})
```

**Ответ:**
```json
{
  "success": true,
  "message": "Блок перемещен"
}
```

### Сохранение блоков (`/api/settings/save-block`)

#### POST - Создание/обновление блока
```javascript
// Отправка через форму
const formData = new FormData();
formData.append('id', 'page-id');
formData.append('blockId', 'block-id');
formData.append('blockName', 'Название блока');
formData.append('type', 'text');
formData.append('order', '1');
formData.append('enabled', 'on');
formData.append('lang', 'ru');
formData.append('content', JSON.stringify(editorData));

fetch('/api/settings/save-block', {
  method: 'POST',
  body: formData
})
```

**Параметры:**
- `id` - ID страницы
- `blockId` - ID блока
- `blockName` - Название блока (новое поле)
- `type` - Тип блока (text, hero, features, gallery, contacts, map, video, forms, custom)
- `order` - Порядок блока (автоматический если пустой)
- `enabled` - Включен ли блок ('on' или отсутствует)
- `lang` - Язык контента ('ru' или 'en')
- `content` - JSON данные EditorJS

**Ответ:** Редирект на страницу редактирования

### Страницы (`/api/settings/pages`)

#### GET - Получение страниц
```javascript
// Все страницы
fetch('/api/settings/pages')

// Конкретная страница
fetch('/api/settings/pages?id=page-id')
```

**Ответ (все страницы):**
```json
{
  "success": true,
  "pages": [...]
}
```

**Ответ (одна страница):**
```json
{
  "success": true,
  "page": {...}
}
```

#### POST - Создание страницы
```javascript
fetch('/api/settings/pages', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    id: 'new-page',
    template: 'default',
    url: { ru: '/new-page', en: '/en/new-page' },
    visible: true,
    seo: {
      title: { ru: 'Заголовок', en: 'Title' },
      description: { ru: 'Описание', en: 'Description' },
      keywords: { ru: 'ключевые слова', en: 'keywords' }
    }
  })
})
```

**Ответ:**
```json
{
  "success": true,
  "message": "Страница создана",
  "page": {...}
}
```

### Сохранение страниц (`/api/settings/save-page`)

#### POST - Создание/обновление страницы
```javascript
// Отправка через форму
const formData = new FormData();
formData.append('id', 'page-id');
formData.append('template', 'default');
formData.append('visible', 'on');
formData.append('url.ru', '/page');
formData.append('url.en', '/en/page');
formData.append('seo.title.ru', 'Заголовок');
formData.append('seo.title.en', 'Title');
// ... другие поля

fetch('/api/settings/save-page', {
  method: 'POST',
  body: formData
})
```

**Ответ:** Редирект на список страниц

## Структура данных

### Блок (BlockConfig)
```typescript
interface BlockDisplaySettings {
  maxWidth?: {
    value: number;
    unit: 'px' | '%';
  };
  alignment?: 'left' | 'center' | 'right';
  padding?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  gridColumns?: number; // Количество элементов в строке для grid-системы
}

interface BlockConfig {
  id: string;           // Уникальный идентификатор
  name?: string;        // Название блока (отображается в админке)
  type: string;         // Тип блока
  enabled: boolean;     // Включен ли блок
  order?: number;       // Порядок отображения
  content: {            // Контент для разных языков
    ru: string;         // JSON данные EditorJS
    en: string;         // JSON данные EditorJS
  };
  displaySettings?: BlockDisplaySettings; // Настройки отображения блока
}
```

### Страница (PageConfig)
```typescript
interface PageConfig {
  id: string;
  template: string;
  blocks: BlockConfig[];
  media: MediaItem[];
  seo: SEOConfig;
  visible: boolean;
  url: LocalizedString;
}
```

## Типы блоков

- **text** - Текстовый блок с базовыми инструментами
- **hero** - Героический блок для главных разделов
- **features** - Блок особенностей/преимуществ
- **gallery** - Галерея изображений
- **contacts** - Контактная информация
- **map** - Карты (Google Maps, Яндекс.Карты)
- **video** - Видео контент (YouTube, Vimeo)
- **forms** - Формы и интерактивные элементы
- **custom** - Универсальный блок со всеми инструментами

## Автоматическое назначение порядка

Если поле `order` не указано или равно 0, система автоматически назначает порядок:
- Находит максимальный порядок среди существующих блоков
- Назначает новому блоку порядок = максимальный + 1

## Коды ошибок

- **401** - Не авторизован
- **400** - Неверные параметры запроса
- **404** - Ресурс не найден
- **500** - Внутренняя ошибка сервера

## Безопасность

Все API endpoints проверяют аутентификацию через функцию `isAuthenticated()` из `src/utils/auth.js`.

## Интеграция

Для интеграции в новый проект:

1. Скопируйте папку `src/pages/api/settings/`
2. Убедитесь, что у вас есть `src/utils/auth.js`
3. Настройте утилиты загрузки/сохранения настроек
4. Используйте API endpoints в ваших компонентах
