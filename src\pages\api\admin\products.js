import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';
import { cleanProductData, validateProductData } from '../../../utils/dataValidation.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const productsPath = path.join(__dirname, '../../../../data/product/products.json');
const productImagesDir = path.join(__dirname, '../../../../public/product');

export async function GET() {
  try {
    const data = await fs.readFile(productsPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения файла' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();

    // Валидация и очистка данных товара
    const validationIssues = validateProductData(body);
    if (validationIssues.length > 0) {
      return new Response(JSON.stringify({
        error: 'Ошибки в данных товара',
        issues: validationIssues
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Очищаем данные от невалидных значений
    const cleanedProduct = cleanProductData(body);

    const data = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(data);

    // Создаем папку для изображений товара
    const productDir = path.join(productImagesDir, cleanedProduct.id);
    try {
      await fs.mkdir(productDir, { recursive: true });
    } catch (error) {
      // Папка может уже существовать, это не критично
    }

    // Добавление статуса по умолчанию, если не указан
    if (!cleanedProduct.status) {
      cleanedProduct.status = 'draft';
    }

    // Добавление нового товара
    products.push(cleanedProduct);

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();

    // Валидация и очистка данных товара
    const validationIssues = validateProductData(body);
    if (validationIssues.length > 0) {
      return new Response(JSON.stringify({
        error: 'Ошибки в данных товара',
        issues: validationIssues
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Очищаем данные от невалидных значений
    const cleanedProduct = cleanProductData(body);

    const data = await fs.readFile(productsPath, 'utf-8');
    let products = JSON.parse(data);

    // Найдем товар для обновления
    const productIndex = products.findIndex(product => product.id === cleanedProduct.id);
    if (productIndex === -1) {
      return new Response(JSON.stringify({ error: 'Товар не найден' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Сохраняем существующий статус, если новый не указан
    if (!cleanedProduct.status && products[productIndex].status) {
      cleanedProduct.status = products[productIndex].status;
    } else if (!cleanedProduct.status) {
      cleanedProduct.status = 'draft';
    }

    // Обновление существующего товара
    products[productIndex] = cleanedProduct;

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('PUT request error:', error);
    return new Response(JSON.stringify({ error: 'Ошибка обновления данных: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID товара обязателен' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = await fs.readFile(productsPath, 'utf-8');
    let products = JSON.parse(data);

    // Удаление товара из JSON
    products = products.filter(product => product.id !== id);

    // Удаление папки с изображениями товара
    const productImageDir = path.join(productImagesDir, id);
    try {
      // Проверяем, существует ли папка
      await fs.access(productImageDir);
      // Удаляем папку рекурсивно со всем содержимым
      await fs.rm(productImageDir, { recursive: true, force: true });
    } catch (error) {
      // Папка может не существовать, это не критично
    }

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка удаления товара:', error);
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
