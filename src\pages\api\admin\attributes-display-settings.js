import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const settingsPath = path.join(__dirname, '../../../../data/admin/attributes-display-settings.json');

export async function GET() {
  try {
    const data = await fs.readFile(settingsPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка чтения настроек отображения:', error);
    
    // Если файл не существует, создаем настройки по умолчанию
    const defaultSettings = {
      tabOrder: [
        "colors",
        "textures", 
        "strength_classes",
        "frost_resistance",
        "water_absorption",
        "size",
        "surfaces",
        "patterns",
        "color_pigments"
      ],
      visibleTabsCount: 6,
      showCounters: true,
      compactMode: false,
      lastUpdated: new Date().toISOString()
    };
    
    try {
      // Создаем директорию если не существует
      await fs.mkdir(path.dirname(settingsPath), { recursive: true });
      await fs.writeFile(settingsPath, JSON.stringify(defaultSettings, null, 2), 'utf-8');
      
      return new Response(JSON.stringify(defaultSettings), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (createError) {
      console.error('Ошибка создания файла настроек:', createError);
      return new Response(JSON.stringify({ error: 'Ошибка создания настроек по умолчанию' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { tabOrder, visibleTabsCount, showCounters, compactMode } = body;

    // Валидация данных
    if (!Array.isArray(tabOrder)) {
      return new Response(JSON.stringify({ error: 'tabOrder должен быть массивом' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (typeof visibleTabsCount !== 'number' || visibleTabsCount < 1 || visibleTabsCount > 20) {
      return new Response(JSON.stringify({ error: 'visibleTabsCount должен быть числом от 1 до 20' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (typeof showCounters !== 'boolean') {
      return new Response(JSON.stringify({ error: 'showCounters должен быть булевым значением' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (typeof compactMode !== 'boolean') {
      return new Response(JSON.stringify({ error: 'compactMode должен быть булевым значением' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создаем объект настроек
    const settings = {
      tabOrder,
      visibleTabsCount,
      showCounters,
      compactMode,
      lastUpdated: new Date().toISOString()
    };

    // Создаем директорию если не существует
    await fs.mkdir(path.dirname(settingsPath), { recursive: true });

    // Сохраняем настройки
    await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true, settings }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка сохранения настроек отображения:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сохранения настроек' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Сброс к настройкам по умолчанию
    const defaultSettings = {
      tabOrder: [
        "colors",
        "textures", 
        "strength_classes",
        "frost_resistance",
        "water_absorption",
        "size",
        "surfaces",
        "patterns",
        "color_pigments"
      ],
      visibleTabsCount: 6,
      showCounters: true,
      compactMode: false,
      lastUpdated: new Date().toISOString()
    };

    // Создаем директорию если не существует
    await fs.mkdir(path.dirname(settingsPath), { recursive: true });

    // Сохраняем настройки по умолчанию
    await fs.writeFile(settingsPath, JSON.stringify(defaultSettings, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true, settings: defaultSettings }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка сброса настроек отображения:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сброса настроек' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
