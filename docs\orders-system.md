# Система сбора заявок

## Описание

Реализована система сбора заявок на заказы товаров и запросы на звонки через формы сайта. Данные сохраняются в JSON файлы и доступны для просмотра в админ панели.

## Структура файлов

### Данные
- `data/orders/orders-product.json` - заявки на товары
- `data/orders/orders-call.json` - запросы на звонки

### API Endpoints
- `POST /api/orders/product` - создание заявки на товары
- `POST /api/orders/call` - создание запроса на звонок
- `GET /api/admin/orders` - получение всех заявок (требует авторизации)
- `PUT /api/admin/orders` - обновление статуса заявки (требует авторизации)
- `DELETE /api/admin/orders` - удаление заявки (требует авторизации)

### Страницы
- `/admin/orders` - страница управления заявками в админ панели

## Формы на сайте

### 1. Форма заказа товаров (корзина)
**Расположение:** `/cart` - компонент `OrderForm.astro`

**Поля:**
- Тип клиента (физическое/юридическое лицо)
- Имя клиента
- Название компании (для юр. лиц)
- Телефон
- Способ оплаты
- Способ доставки
- Комментарий
- Товары из корзины

**Отправка:** POST `/api/orders/product`

### 2. Форма обратной связи (запрос на звонок)
**Расположение:** Footer сайта - компонент `ContactForm.astro`

**Поля:**
- Имя
- Телефон
- Сообщение

**Отправка:** POST `/api/orders/call`

## Админ панель

### Доступ
- URL: `/admin/orders`
- Требует авторизации в админ панели
- Пункт меню "Заявки" в боковой навигации

### Функциональность
- Просмотр всех заявок (товары + звонки)
- Фильтрация по типу (товары/звонки)
- Фильтрация по статусу (новые/в обработке/завершенные/отмененные)
- Изменение статуса заявки
- Удаление заявок
- Статистика по заявкам

### Статусы заявок
- `new` - Новая
- `processing` - В обработке
- `completed` - Завершена
- `cancelled` - Отменена

## Структура данных

### Заявка на товары
```json
{
  "id": "ORD-1234567890-123",
  "type": "product",
  "clientType": "individual",
  "clientName": "Иван Иванов",
  "companyName": null,
  "phone": "+375 (29) 123-45-67",
  "paymentMethod": "cash",
  "deliveryMethod": "pickup",
  "comment": "Комментарий к заказу",
  "items": [
    {
      "id": "product-id",
      "name": "Название товара",
      "price": 100.00,
      "quantity": 2,
      "currency": "BYN"
    }
  ],
  "totalAmount": 200.00,
  "status": "new",
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z"
}
```

### Запрос на звонок
```json
{
  "id": "CALL-1234567890-123",
  "type": "call",
  "name": "Иван Иванов",
  "phone": "+375 (29) 123-45-67",
  "message": "Хочу узнать о доставке",
  "status": "new",
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z"
}
```

## Безопасность

### Защита файлов данных
JSON файлы с заявками находятся в папке `data/orders/` и не доступны напрямую через веб-сервер благодаря настройкам Astro. Доступ к данным возможен только через API endpoints.

### Аутентификация
Все API endpoints для управления заявками в админ панели защищены функцией `isAuthenticated()` и требуют авторизации администратора.

## Тестирование

### Тестирование форм
1. Перейти на главную страницу сайта
2. Заполнить форму обратной связи в футере
3. Добавить товары в корзину и оформить заказ на странице `/cart`

### Тестирование админ панели
1. Войти в админ панель (`/admin/login`)
2. Перейти в раздел "Заявки" (`/admin/orders`)
3. Проверить отображение заявок
4. Протестировать фильтры и изменение статусов

## Возможные улучшения

1. **Email уведомления** - отправка уведомлений администратору о новых заявках
2. **Экспорт данных** - возможность экспорта заявок в Excel/CSV
3. **База данных** - миграция с JSON файлов на полноценную БД
4. **Валидация** - более строгая валидация данных форм
5. **Логирование** - детальное логирование операций с заявками
6. **Архивирование** - автоматическое архивирование старых заявок

## Примечания

- Система использует JSON файлы как временное решение для хранения данных
- ID заявок генерируются автоматически с префиксами ORD- (товары) и CALL- (звонки)
- Все даты сохраняются в формате ISO 8601
- Система поддерживает белорусские номера телефонов в формате +375
