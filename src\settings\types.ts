// Тип для локализованных строк
export type LocalizedString = { [lang: string]: string };

// Тип для локализованного контента (например, блоки с разной структурой)
export type LocalizedContent<T = any> = { [lang: string]: T };

// SEO-настройки страницы
export interface SEOConfig {
  title: LocalizedString;
  description: LocalizedString;
  keywords?: LocalizedString;
}

// Медиа-элемент (может быть расширен при необходимости)
export interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'pdf';
  url: string;
  category?: string;
  alt?: LocalizedString;
}

// Элемент в grid-системе (теперь ссылается на блоки EditorJS)
export interface GridItem {
  id: string; // ID блока из EditorJS
  type: string; // Тип блока EditorJS (paragraph, header, list, etc.)
  order: number; // Порядок в grid
  gridColumn?: string; // Например: "1 / 3" или "span 2"
  gridRow?: string; // Например: "1 / 2" или "span 1"
  editorData?: any; // Данные блока из EditorJS
  containerId?: string; // ID контейнера, к которому принадлежит элемент (для режима container)
}

// Контейнер в grid-системе (для режима container)
export interface GridContainer {
  id: string; // Уникальный ID контейнера
  order: number; // Порядок контейнера в общем списке
  isActive: boolean; // Активен ли контейнер для редактирования
  items: GridItem[]; // Элементы внутри контейнера
  gridColumn?: string; // Позиция контейнера в grid
  gridRow?: string; // Позиция контейнера в grid
  settings?: {
    // Настройки контейнера (могут переопределять глобальные)
    padding?: { value: number; unit: 'px' | 'rem' };
    margin?: { value: number; unit: 'px' | 'rem' };
    backgroundColor?: string;
    border?: {
      width: number;
      style: 'solid' | 'dashed' | 'dotted';
      color: string;
    };
  };
}

// Настройки grid-системы для новичков
export interface GridBeginnerSettings {
  columns: number; // Количество колонок (1-12)
  rows?: number; // Количество строк (авто если не указано)
  gap: {
    value: number;
    unit: 'px' | 'rem';
  };
  itemAlignment: 'start' | 'center' | 'end' | 'stretch';
  contentAlignment: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
}

// Настройки grid-системы для профи
export interface GridProSettings {
  gridTemplateColumns: string; // Например: "repeat(3, 1fr)" или "200px 1fr 100px"
  gridTemplateRows?: string; // Например: "repeat(2, 100px)" или "auto 1fr"
  gridTemplateAreas?: string; // Например: '"header header" "sidebar main"'
  gap: {
    value: number;
    unit: 'px' | 'rem';
  };
  alignItems: 'start' | 'center' | 'end' | 'stretch';
  justifyItems: 'start' | 'center' | 'end' | 'stretch';
  alignContent: 'start' | 'center' | 'end' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
  justifyContent: 'start' | 'center' | 'end' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
}

// Основные настройки grid-системы (упрощенная версия)
export interface GridSystemSettings {
  enabled: boolean;
  mode: 'simple' | 'container'; // Простой режим или режим контейнеров

  // Основные настройки Grid
  columns: number;
  gap: {
    value: number;
    unit: 'px' | 'rem' | 'em' | '%';
  };
  itemAlignment: 'start' | 'center' | 'end' | 'stretch';
  contentAlignment: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';

  // Дополнительные настройки
  autoRows?: string;
  justifyItems?: 'start' | 'center' | 'end' | 'stretch';
  alignContent?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
  justifyContent?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';

  // Адаптивные настройки
  responsive?: {
    desktop: {
      columns: number;
      gap?: {
        value: number;
        unit: 'px' | 'rem' | 'em' | '%';
      };
    };
    tablet: {
      columns: number;
      gap?: {
        value: number;
        unit: 'px' | 'rem' | 'em' | '%';
      };
    };
    mobile: {
      columns: number;
      gap?: {
        value: number;
        unit: 'px' | 'rem' | 'em' | '%';
      };
    };
  };

  // Элементы Grid (для режима simple)
  items?: GridItem[];

  // Контейнеры Grid (для режима container)
  containers?: GridContainer[];

  // Активный контейнер (для режима container)
  activeContainerId?: string;

  previewActive: boolean;
  editorBlocks?: any[];
}

// Настройки отображения блока
export interface BlockDisplaySettings {
  maxWidth?: {
    value: number;
    unit: 'px' | '%';
  };
  alignment?: 'left' | 'center' | 'right';
  padding?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
    unit: 'px' | '%';
  };
  gridSystem?: GridSystemSettings;
}

// Блок на странице
export interface BlockConfig {
  id: string;
  name?: string;
  type: string;
  enabled: boolean;
  order?: number;
  content: LocalizedContent;
  displaySettings?: BlockDisplaySettings;
}

// Конфиг одной страницы
export interface PageConfig {
  id: string;
  template: string;
  blocks: BlockConfig[];
  media: MediaItem[];
  seo: SEOConfig;
  visible: boolean;
  url: LocalizedString; // Латинские URL для всех языков
}

// Конфиг меню (например, для header/footer)
export interface MenuItem {
  label: LocalizedString;
  url: LocalizedString;
  order?: number;
}

// Header/Footer конфиг
export interface HeaderFooterConfig {
  template: string;
  menu: MenuItem[];
  [key: string]: any;
}

// Основной конфиг страниц
export interface PagesSettings {
  pages: PageConfig[];
  header: HeaderFooterConfig;
  footer: HeaderFooterConfig;
}
