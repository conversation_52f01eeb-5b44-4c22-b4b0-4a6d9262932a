// API для получения мета-данных ссылок для EditorJS LinkTool
import fetch from 'node-fetch';

export async function GET({ request }) {
  try {
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');

    if (!targetUrl) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'URL не указан'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем валидность URL
    let validUrl;
    try {
      validUrl = new URL(targetUrl);
    } catch (error) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Некорректный URL'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Получаем HTML страницы
    const response = await fetch(targetUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; EditorJS LinkTool)'
      },
      timeout: 10000
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const html = await response.text();

    // Извлекаем мета-данные
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const descriptionMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i) ||
                            html.match(/<meta[^>]*content=["\']([^"']+)["\'][^>]*name=["\']description["\'][^>]*>/i);
    const imageMatch = html.match(/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i) ||
                      html.match(/<meta[^>]*content=["\']([^"']+)["\'][^>]*property=["\']og:image["\'][^>]*>/i);

    const title = titleMatch ? titleMatch[1].trim() : validUrl.hostname;
    const description = descriptionMatch ? descriptionMatch[1].trim() : '';
    const image = imageMatch ? imageMatch[1].trim() : '';

    return new Response(JSON.stringify({
      success: 1,
      link: targetUrl,
      meta: {
        title: title,
        description: description,
        image: {
          url: image
        }
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при получении мета-данных URL:', error);
    
    return new Response(JSON.stringify({
      success: 0,
      message: 'Ошибка при получении данных страницы'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  return GET({ request });
}
