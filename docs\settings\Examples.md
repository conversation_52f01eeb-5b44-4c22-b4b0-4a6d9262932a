# Примеры использования системы настроек страниц

## 📝 Создание простой страницы

### 1. Создание страницы через админку

```javascript
// Данные для создания страницы
const pageData = {
  id: 'about-us',
  template: 'default',
  url: { 
    ru: '/o-nas', 
    en: '/about-us' 
  },
  visible: true,
  seo: {
    title: { 
      ru: 'О нас - Наша компания', 
      en: 'About Us - Our Company' 
    },
    description: { 
      ru: 'Узнайте больше о нашей компании', 
      en: 'Learn more about our company' 
    },
    keywords: { 
      ru: 'о компании, история, команда', 
      en: 'about company, history, team' 
    }
  }
};
```

### 2. Добавление текстового блока

```javascript
// Данные EditorJS для текстового блока
const textBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "header-1",
      type: "header",
      data: {
        text: "О нашей компании",
        level: 1
      }
    },
    {
      id: "paragraph-1",
      type: "paragraph",
      data: {
        text: "Мы работаем на рынке уже более 10 лет и предоставляем качественные услуги."
      }
    },
    {
      id: "list-1",
      type: "list",
      data: {
        style: "unordered",
        items: [
          { content: "Высокое качество продукции", items: [] },
          { content: "Индивидуальный подход", items: [] },
          { content: "Быстрая доставка", items: [] }
        ]
      }
    }
  ],
  version: "2.31.0"
};

// Создание блока
const blockData = {
  id: 'about-text-1',
  name: 'Основная информация о компании',
  type: 'text',
  order: 1,
  enabled: true,
  content: {
    ru: JSON.stringify(textBlockData),
    en: JSON.stringify(textBlockDataEn)
  }
};
```

## 🎨 Создание Hero блока

```javascript
const heroBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "hero-header",
      type: "header",
      data: {
        text: "Добро пожаловать в нашу компанию",
        level: 1
      }
    },
    {
      id: "hero-image",
      type: "image",
      data: {
        file: {
          url: "/uploads/hero-image.jpg",
          name: "hero-image.jpg"
        },
        caption: "Наш офис",
        withBorder: false,
        withBackground: false,
        stretched: true
      }
    },
    {
      id: "hero-paragraph",
      type: "paragraph",
      data: {
        text: "Мы создаем инновационные решения для вашего бизнеса."
      }
    }
  ],
  version: "2.31.0"
};
```

## 📊 Создание блока с таблицей

```javascript
const tableBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "table-header",
      type: "header",
      data: {
        text: "Наши услуги и цены",
        level: 2
      }
    },
    {
      id: "services-table",
      type: "table",
      data: {
        withHeadings: true,
        content: [
          ["Услуга", "Описание", "Цена"],
          ["Консультация", "Первичная консультация", "Бесплатно"],
          ["Разработка", "Создание решения", "От 50 000 ₽"],
          ["Поддержка", "Техническая поддержка", "От 10 000 ₽/мес"]
        ]
      }
    }
  ],
  version: "2.31.0"
};
```

## 🗺️ Создание блока с картой

```javascript
const mapBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "map-header",
      type: "header",
      data: {
        text: "Как нас найти",
        level: 2
      }
    },
    {
      id: "map-embed",
      type: "raw",
      data: {
        html: `<iframe 
          src="https://yandex.ru/map-widget/v1/?um=constructor%3A..." 
          width="100%" 
          height="400" 
          frameborder="0">
        </iframe>`
      }
    },
    {
      id: "map-paragraph",
      type: "paragraph",
      data: {
        text: "Мы находимся в центре города, рядом с метро."
      }
    }
  ],
  version: "2.31.0"
};
```

## 🎬 Создание блока с видео

```javascript
const videoBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "video-header",
      type: "header",
      data: {
        text: "Посмотрите наше видео",
        level: 2
      }
    },
    {
      id: "video-embed",
      type: "embed",
      data: {
        service: "youtube",
        source: "https://www.youtube.com/watch?v=VIDEO_ID",
        embed: "https://www.youtube.com/embed/VIDEO_ID",
        width: 580,
        height: 320,
        caption: "Презентация нашей компании"
      }
    }
  ],
  version: "2.31.0"
};
```

## 🛠️ Создание Custom блока

```javascript
const customBlockData = {
  time: Date.now(),
  blocks: [
    {
      id: "custom-header",
      type: "header",
      data: {
        text: "Комплексный раздел",
        level: 1
      }
    },
    {
      id: "custom-quote",
      type: "quote",
      data: {
        text: "Качество - это не случайность, это всегда результат высокого намерения.",
        caption: "Джон Раскин"
      }
    },
    {
      id: "custom-code",
      type: "code",
      data: {
        code: `function calculatePrice(service, duration) {
  const basePrice = services[service].price;
  return basePrice * duration;
}`
      }
    },
    {
      id: "custom-button",
      type: "button",
      data: {
        text: "Связаться с нами",
        link: "/contacts"
      }
    },
    {
      id: "custom-delimiter",
      type: "delimiter",
      data: {}
    },
    {
      id: "custom-html",
      type: "raw",
      data: {
        html: `<div class="custom-widget">
          <h3>Специальное предложение</h3>
          <p>Скидка 20% на первый заказ!</p>
        </div>`
      }
    }
  ],
  version: "2.31.0"
};
```

## 📱 Программное создание блоков

### Создание блока через API

```javascript
async function createBlock(pageId, blockData) {
  const formData = new FormData();
  formData.append('id', pageId);
  formData.append('blockId', blockData.id);
  formData.append('blockName', blockData.name);
  formData.append('type', blockData.type);
  formData.append('order', blockData.order || '');
  formData.append('enabled', blockData.enabled ? 'on' : '');
  formData.append('lang', 'ru');
  formData.append('content', blockData.content.ru);

  const response = await fetch('/api/settings/save-block', {
    method: 'POST',
    body: formData
  });

  return response.ok;
}

// Использование
const success = await createBlock('about-us', {
  id: 'about-hero',
  name: 'Главный баннер',
  type: 'hero',
  order: 1,
  enabled: true,
  content: {
    ru: JSON.stringify(heroBlockData)
  }
});
```

### Массовое создание блоков

```javascript
async function createMultipleBlocks(pageId, blocks) {
  const results = [];
  
  for (const block of blocks) {
    try {
      const success = await createBlock(pageId, block);
      results.push({ block: block.id, success });
    } catch (error) {
      results.push({ block: block.id, success: false, error });
    }
  }
  
  return results;
}

// Создание полной страницы
const pageBlocks = [
  {
    id: 'hero-1',
    name: 'Главный баннер',
    type: 'hero',
    order: 1,
    enabled: true,
    content: { ru: JSON.stringify(heroBlockData) }
  },
  {
    id: 'features-1',
    name: 'Наши преимущества',
    type: 'features',
    order: 2,
    enabled: true,
    content: { ru: JSON.stringify(featuresBlockData) }
  },
  {
    id: 'contacts-1',
    name: 'Контактная информация',
    type: 'contacts',
    order: 3,
    enabled: true,
    content: { ru: JSON.stringify(contactsBlockData) }
  }
];

const results = await createMultipleBlocks('about-us', pageBlocks);
```

## 🔄 Обновление существующих блоков

```javascript
async function updateBlockContent(pageId, blockId, newContent) {
  const formData = new FormData();
  formData.append('id', pageId);
  formData.append('blockId', blockId);
  formData.append('lang', 'ru');
  formData.append('content', JSON.stringify(newContent));

  const response = await fetch('/api/settings/save-block', {
    method: 'POST',
    body: formData
  });

  return response.ok;
}
```

## 📋 Получение данных страницы

```javascript
async function getPageData(pageId) {
  const response = await fetch(`/api/settings/pages?id=${pageId}`);
  const data = await response.json();
  
  if (data.success) {
    return data.page;
  }
  
  throw new Error('Страница не найдена');
}

// Использование
const pageData = await getPageData('about-us');
console.log('Блоки страницы:', pageData.blocks);
```

## 🎯 Интеграция с фронтендом

### Рендеринг блоков на странице

```astro
---
// pages/[slug].astro
import { loadPageSettings } from '../settings/utils/settingsLoader.js';

const { slug } = Astro.params;
const settings = await loadPageSettings();
const page = settings.pages?.find(p => p.url.ru === `/${slug}`);

if (!page) {
  return Astro.redirect('/404');
}

// Сортируем блоки по порядку
const sortedBlocks = page.blocks
  ?.filter(block => block.enabled)
  ?.sort((a, b) => (a.order || 0) - (b.order || 0)) || [];
---

<html>
<head>
  <title>{page.seo.title.ru}</title>
  <meta name="description" content={page.seo.description.ru} />
  <meta name="keywords" content={page.seo.keywords.ru} />
</head>
<body>
  {sortedBlocks.map(block => (
    <div class={`block block-${block.type}`} data-block-id={block.id}>
      <!-- Рендеринг контента блока -->
      <div set:html={renderEditorJSContent(block.content.ru)} />
    </div>
  ))}
</body>
</html>
```

Эти примеры показывают основные сценарии использования системы настроек страниц для создания богатого контента с помощью блочного редактора.
