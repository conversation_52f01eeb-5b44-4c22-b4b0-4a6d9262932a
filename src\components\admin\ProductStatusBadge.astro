---
import type { ProductStatus } from '../../types';

interface Props {
  status: ProductStatus;
  size?: 'sm' | 'md' | 'lg';
  showDescription?: boolean;
  className?: string;
}

const {
  status,
  size = 'md',
  showDescription = false,
  className = ''
} = Astro.props;

// Конфигурация статусов
const statusConfig = {
  draft: {
    label: 'Черновик',
    description: 'Товар создается, но не отображается для пользователей',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-300'
  },
  published: {
    label: 'Опубликован',
    description: 'Товар доступен для просмотра на сайте',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    borderColor: 'border-green-300'
  },
  unpublished: {
    label: 'Не опубликован',
    description: 'Товар не отображается на сайте',
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    borderColor: 'border-red-300'
  }
};

// Размеры компонента
const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-3 py-2',
  lg: 'text-base px-4 py-3'
};

const config = statusConfig[status];
---

<div class={`
  product-status-badge inline-flex items-center
  ${sizeClasses[size]}
  ${config.bgColor} ${config.textColor} ${config.borderColor}
  border rounded-full font-medium
  ${className}
`}>
  <span>{config.label}</span>
  {showDescription && (
    <span class="ml-2 text-xs opacity-75">
      {config.description}
    </span>
  )}
</div>
