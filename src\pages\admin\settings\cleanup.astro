---
import AdminLayout from '../../../layouts/AdminLayout.astro';
---

<AdminLayout title="Очистка настроек">
  <div class="max-w-4xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Очистка настроек</h1>
    <p class="text-gray-600 mb-6">Проверка и очистка дублирующих записей в настройках страниц.</p>
    
    <div class="space-y-4">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold text-blue-900 mb-2">Проверка дубликатов</h2>
        <p class="text-blue-700 mb-4">Проверить наличие дублирующих страниц без их удаления.</p>
        <button id="check-duplicates" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded shadow transition">
          Проверить дубликаты
        </button>
      </div>
      
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold text-yellow-900 mb-2">Очистка дубликатов</h2>
        <p class="text-yellow-700 mb-4">Удалить все дублирующие записи и страницы без ID. <strong>Это действие нельзя отменить!</strong></p>
        <button id="cleanup-duplicates" class="bg-yellow-600 hover:bg-yellow-700 text-white font-semibold px-4 py-2 rounded shadow transition">
          Очистить дубликаты
        </button>
      </div>
      
      <div id="results" class="hidden">
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Результаты</h3>
          <div id="results-content" class="text-gray-700"></div>
        </div>
      </div>
    </div>
    
    <div class="mt-8">
      <a href="/admin/settings/pages" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">
        ← Назад к страницам
      </a>
    </div>
  </div>
</AdminLayout>

<script is:inline>
document.addEventListener('DOMContentLoaded', function() {
  const checkButton = document.getElementById('check-duplicates');
  const cleanupButton = document.getElementById('cleanup-duplicates');
  const resultsDiv = document.getElementById('results');
  const resultsContent = document.getElementById('results-content');
  
  function showResults(data, isCleanup = false) {
    resultsDiv.classList.remove('hidden');
    
    let html = '';
    if (data.success) {
      if (isCleanup) {
        html = `
          <div class="text-green-700">
            <p><strong>✅ Очистка завершена успешно!</strong></p>
            <p>Исходное количество страниц: ${data.originalCount}</p>
            <p>Итоговое количество страниц: ${data.finalCount}</p>
            <p>Удалено записей: ${data.removedCount}</p>
          </div>
        `;
      } else {
        if (data.hasDuplicates) {
          html = `
            <div class="text-yellow-700">
              <p><strong>⚠️ Найдены дублирующие записи!</strong></p>
              <p>Всего страниц: ${data.totalPages}</p>
              <p>Уникальных страниц: ${data.uniquePages}</p>
              <p>Страниц без ID: ${data.pagesWithoutId}</p>
              ${data.duplicateIds.length > 0 ? `<p>Дублирующие ID: ${data.duplicateIds.join(', ')}</p>` : ''}
            </div>
          `;
        } else {
          html = `
            <div class="text-green-700">
              <p><strong>✅ Дубликаты не найдены!</strong></p>
              <p>Всего страниц: ${data.totalPages}</p>
              <p>Все страницы имеют уникальные ID.</p>
            </div>
          `;
        }
      }
    } else {
      html = `
        <div class="text-red-700">
          <p><strong>❌ Ошибка:</strong> ${data.error}</p>
        </div>
      `;
    }
    
    resultsContent.innerHTML = html;
  }
  
  checkButton.addEventListener('click', async function() {
    checkButton.disabled = true;
    checkButton.textContent = 'Проверка...';
    
    try {
      const response = await fetch('/api/settings/cleanup');
      const data = await response.json();
      showResults(data, false);
    } catch (error) {
      showResults({ success: false, error: 'Ошибка сети: ' + error.message }, false);
    } finally {
      checkButton.disabled = false;
      checkButton.textContent = 'Проверить дубликаты';
    }
  });
  
  cleanupButton.addEventListener('click', async function() {
    if (!confirm('Вы уверены, что хотите удалить все дублирующие записи? Это действие нельзя отменить!')) {
      return;
    }
    
    cleanupButton.disabled = true;
    cleanupButton.textContent = 'Очистка...';
    
    try {
      const response = await fetch('/api/settings/cleanup', { method: 'POST' });
      const data = await response.json();
      showResults(data, true);
    } catch (error) {
      showResults({ success: false, error: 'Ошибка сети: ' + error.message }, true);
    } finally {
      cleanupButton.disabled = false;
      cleanupButton.textContent = 'Очистить дубликаты';
    }
  });
});
</script>
