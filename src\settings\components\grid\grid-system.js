/**
 * Управление компактной Grid-системой с интеграцией EditorJS
 */
class CompactGridSystemManager {
  constructor(blockId) {
    this.blockId = blockId;
    this.container = document.querySelector(`.compact-grid-manager[data-block-id="${blockId}"]`);
    this.previewContainer = document.getElementById(`grid-preview-${blockId}`);

    if (!this.container) {
      console.error(`Compact Grid container not found for block ${blockId}`);
      return;
    }

    this.init();
  }

  init() {
    this.bindEvents();
    this.updatePreview();
  }

  bindEvents() {
    // Переключатель включения/выключения Grid
    const enableToggle = this.container.querySelector('input[name="gridEnabled"]');
    if (enableToggle) {
      enableToggle.addEventListener('change', (e) => {
        this.toggleGridSystem(e.target.checked);
      });
    }

    // Переключатель режима (новичок/профи)
    const modeRadios = this.container.querySelectorAll('input[name^="gridMode"]');
    modeRadios.forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.switchMode(e.target.value);
      });
    });

    // Настройки для новичков
    this.bindBeginnerEvents();

    // Настройки для профи
    this.bindProEvents();

    // Управление элементами
    this.bindItemEvents();
  }

  bindBeginnerEvents() {
    const settingsInputs = this.container.querySelectorAll('.beginner-settings select, .beginner-settings input');
    settingsInputs.forEach(input => {
      input.addEventListener('change', () => {
        this.updatePreview();
        this.updateItemsData(this.getGridItems());
      });
    });
  }

  bindProEvents() {
    const proInputs = this.container.querySelectorAll('.pro-settings input, .pro-settings select');
    proInputs.forEach(input => {
      input.addEventListener('input', () => {
        this.updatePreview();
        this.updateItemsData(this.getGridItems());
      });
    });
  }

  bindItemEvents() {
    // Добавление нового элемента
    const addBtn = this.container.querySelector('.add-item-btn');
    if (addBtn) {
      addBtn.addEventListener('click', () => {
        this.addGridItem();
      });
    }

    // Удаление элементов
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('delete-item-btn')) {
        const itemId = e.target.dataset.itemId;
        this.deleteGridItem(itemId);
      }
    });

    // Перемещение элементов
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('move-up-btn')) {
        const itemId = e.target.dataset.itemId;
        this.moveGridItem(itemId, 'up');
      } else if (e.target.classList.contains('move-down-btn')) {
        const itemId = e.target.dataset.itemId;
        this.moveGridItem(itemId, 'down');
      }
    });

    // Переключение языков
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('lang-tab')) {
        const lang = e.target.dataset.lang;
        const itemId = e.target.dataset.itemId;
        this.switchLanguage(itemId, lang);
      }
    });
  }

  toggleGridSystem(enabled) {
    const settingsContainer = this.container.querySelector('.grid-settings-container');
    if (settingsContainer) {
      settingsContainer.style.display = enabled ? '' : 'none';
    }
    this.updatePreview();
    this.updateItemsData(this.getGridItems());
  }

  switchMode(mode) {
    const beginnerSettings = this.container.querySelector('.beginner-settings');
    const proSettings = this.container.querySelector('.pro-settings');
    const proOnlyElements = this.container.querySelectorAll('.pro-only');

    if (mode === 'beginner') {
      beginnerSettings.style.display = '';
      proSettings.style.display = 'none';
      proOnlyElements.forEach(el => el.style.display = 'none');
    } else {
      beginnerSettings.style.display = 'none';
      proSettings.style.display = '';
      proOnlyElements.forEach(el => el.style.display = '');
    }

    this.updatePreview();
  }

  updatePreview() {
    if (!this.previewContainer) return;

    const enabled = this.container.querySelector('input[name="gridEnabled"]')?.checked;
    if (!enabled) {
      this.previewContainer.innerHTML = '<p class="text-gray-500 text-center">Grid отключен</p>';
      return;
    }

    const mode = this.container.querySelector('input[name^="gridMode"]:checked')?.value || 'beginner';
    const items = this.getGridItems();

    if (mode === 'beginner') {
      this.updateBeginnerPreview(items);
    } else {
      this.updateProPreview(items);
    }

    // Рендерим элементы EditorJS
    if (window.renderEditorJSGridItems && items.length > 0) {
      setTimeout(() => {
        window.renderEditorJSGridItems(items, this.blockId);
      }, 100);
    }
  }

  updateBeginnerPreview(items) {
    const columns = parseInt(this.container.querySelector('select[name="beginnerColumns"]')?.value || '2');
    const gapValue = parseFloat(this.container.querySelector('input[name="beginnerGapValue"]')?.value || '1');
    const gapUnit = this.container.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem';
    const itemAlignment = this.container.querySelector('select[name="beginnerItemAlignment"]')?.value || 'stretch';
    const contentAlignment = this.container.querySelector('select[name="beginnerContentAlignment"]')?.value || 'start';

    this.previewContainer.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    this.previewContainer.style.gap = `${gapValue}${gapUnit}`;
    this.previewContainer.style.alignItems = itemAlignment;
    this.previewContainer.style.justifyContent = contentAlignment;

    this.renderPreviewItems(items);
  }

  updateProPreview(items) {
    const gridTemplateColumns = this.container.querySelector('input[name="proGridTemplateColumns"]')?.value || 'repeat(2, 1fr)';
    const gridTemplateRows = this.container.querySelector('input[name="proGridTemplateRows"]')?.value || '';
    const gapValue = parseFloat(this.container.querySelector('input[name="proGapValue"]')?.value || '1');
    const gapUnit = this.container.querySelector('select[name="proGapUnit"]')?.value || 'rem';
    const alignItems = this.container.querySelector('select[name="proAlignItems"]')?.value || 'stretch';
    const justifyItems = this.container.querySelector('select[name="proJustifyItems"]')?.value || 'stretch';

    this.previewContainer.style.gridTemplateColumns = gridTemplateColumns;
    this.previewContainer.style.gridTemplateRows = gridTemplateRows;
    this.previewContainer.style.gap = `${gapValue}${gapUnit}`;
    this.previewContainer.style.alignItems = alignItems;
    this.previewContainer.style.justifyItems = justifyItems;

    this.renderPreviewItems(items);
  }

  renderPreviewItems(items) {
    const maxPreviewItems = Math.min(items.length, 6); // Показываем максимум 6 элементов в превью
    
    this.previewContainer.innerHTML = '';
    
    for (let i = 0; i < maxPreviewItems; i++) {
      const item = items[i];
      const previewItem = document.createElement('div');
      previewItem.className = 'preview-item';
      
      const content = item?.content?.ru || `Элемент ${i + 1}`;
      const shortContent = content.length > 30 ? content.substring(0, 30) + '...' : content;
      
      previewItem.textContent = shortContent || `Элемент ${i + 1}`;
      
      // Применяем индивидуальные настройки позиционирования для профи режима
      const mode = this.container.querySelector('input[name^="gridMode"]:checked')?.value;
      if (mode === 'pro' && item) {
        if (item.gridColumn) {
          previewItem.style.gridColumn = item.gridColumn;
        }
        if (item.gridRow) {
          previewItem.style.gridRow = item.gridRow;
        }
      }
      
      this.previewContainer.appendChild(previewItem);
    }

    if (items.length > maxPreviewItems) {
      const moreItem = document.createElement('div');
      moreItem.className = 'preview-item';
      moreItem.style.opacity = '0.6';
      moreItem.textContent = `+${items.length - maxPreviewItems} еще`;
      this.previewContainer.appendChild(moreItem);
    }
  }

  getGridItems() {
    const systemData = this.container.querySelector('input[name="gridSystemData"]')?.value;
    if (systemData) {
      try {
        const data = JSON.parse(systemData);
        return data.items || [];
      } catch (e) {
        console.error('Error parsing grid system data:', e);
      }
    }
    return [];
  }

  addGridItem() {
    const items = this.getGridItems();
    const maxItems = 12;

    if (items.length >= maxItems) {
      alert(`Максимальное количество элементов: ${maxItems}`);
      return;
    }

    const newItem = {
      id: 'item_' + Math.random().toString(36).substr(2, 9),
      content: { ru: '', en: '' },
      order: items.length + 1,
      gridColumn: undefined,
      gridRow: undefined
    };

    items.push(newItem);
    this.updateItemsData(items);
    this.renderItemsManager(items);
    this.updatePreview();
  }

  deleteGridItem(itemId) {
    const items = this.getGridItems();

    if (items.length <= 1) {
      alert('Должен остаться хотя бы один элемент');
      return;
    }

    const filteredItems = items.filter(item => item.id !== itemId);

    // Обновляем порядок элементов
    filteredItems.forEach((item, index) => {
      item.order = index + 1;
    });

    this.updateItemsData(filteredItems);
    this.renderItemsManager(filteredItems);
    this.updatePreview();
  }

  moveGridItem(itemId, direction) {
    const items = this.getGridItems();
    const itemIndex = items.findIndex(item => item.id === itemId);

    if (itemIndex === -1) return;

    const newIndex = direction === 'up' ? itemIndex - 1 : itemIndex + 1;

    if (newIndex < 0 || newIndex >= items.length) return;

    // Меняем местами элементы
    [items[itemIndex], items[newIndex]] = [items[newIndex], items[itemIndex]];

    // Обновляем порядок
    items.forEach((item, index) => {
      item.order = index + 1;
    });

    this.updateItemsData(items);
    this.renderItemsManager(items);
    this.updatePreview();
  }

  updateItemsData(items) {
    const hiddenInput = this.container.querySelector('input[name="gridSystemData"]');
    if (hiddenInput) {
      // Get all current settings to ensure the entire state is persisted.
      const settings = this.getSettings();
      // The 'items' parameter contains the most up-to-date item list, so we
      // overwrite the one retrieved by getSettings().
      settings.items = items;
      hiddenInput.value = JSON.stringify(settings);
    }
  }

  renderItemsManager(items) {
    // Перерендериваем менеджер элементов
    const itemsContainer = this.container.querySelector('.items-list');
    if (!itemsContainer) return;

    itemsContainer.innerHTML = '';

    items.forEach((item, index) => {
      const itemElement = this.createItemElement(item, index, items.length);
      itemsContainer.appendChild(itemElement);
    });

    // Обновляем кнопку добавления
    this.updateAddButton(items.length);
  }

  createItemElement(item, index, totalItems) {
    const itemDiv = document.createElement('div');
    itemDiv.className = 'grid-item-editor';
    itemDiv.dataset.itemId = item.id;

    itemDiv.innerHTML = `
      <div class="item-header flex items-center justify-between mb-3">
        <h5 class="text-sm font-medium text-gray-800">Элемент ${index + 1}</h5>
        <div class="item-actions flex space-x-2">
          ${index > 0 ? `<button type="button" class="move-up-btn text-gray-500 hover:text-gray-700" data-item-id="${item.id}" title="Переместить вверх">↑</button>` : ''}
          ${index < totalItems - 1 ? `<button type="button" class="move-down-btn text-gray-500 hover:text-gray-700" data-item-id="${item.id}" title="Переместить вниз">↓</button>` : ''}
          ${totalItems > 1 ? `<button type="button" class="delete-item-btn text-red-500 hover:text-red-700" data-item-id="${item.id}" title="Удалить элемент">✕</button>` : ''}
        </div>
      </div>

      <div class="item-content mb-3">
        <div class="language-tabs mb-2">
          <button type="button" class="lang-tab active" data-lang="ru" data-item-id="${item.id}">Русский</button>
          <button type="button" class="lang-tab" data-lang="en" data-item-id="${item.id}">English</button>
        </div>

        <div class="lang-content active" data-lang="ru" data-item-id="${item.id}">
          <textarea name="item-content-ru-${item.id}" placeholder="Введите контент на русском языке..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4">${item.content.ru}</textarea>
        </div>

        <div class="lang-content" data-lang="en" data-item-id="${item.id}" style="display: none;">
          <textarea name="item-content-en-${item.id}" placeholder="Enter content in English..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4">${item.content.en}</textarea>
        </div>
      </div>

      <div class="item-positioning pro-only" style="display: none;">
        <h6 class="text-xs font-medium text-gray-700 mb-2">Позиционирование в Grid</h6>
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-xs text-gray-600 mb-1">Grid Column</label>
            <input type="text" name="item-grid-column-${item.id}" value="${item.gridColumn || ''}" placeholder="1 / 3 или span 2" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
          </div>
          <div>
            <label class="block text-xs text-gray-600 mb-1">Grid Row</label>
            <input type="text" name="item-grid-row-${item.id}" value="${item.gridRow || ''}" placeholder="1 / 2 или span 1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
          </div>
        </div>
      </div>
    `;

    // Добавляем обработчики событий для текстовых полей
    const textareas = itemDiv.querySelectorAll('textarea');
    textareas.forEach(textarea => {
      textarea.addEventListener('input', () => {
        this.updateItemContent(item.id, textarea);
      });
    });

    // Добавляем обработчики для полей позиционирования
    const positionInputs = itemDiv.querySelectorAll('input[name^="item-grid-"]');
    positionInputs.forEach(input => {
      input.addEventListener('input', () => {
        this.updateItemPosition(item.id, input);
      });
    });

    return itemDiv;
  }

  updateItemContent(itemId, textarea) {
    const items = this.getGridItems();
    const item = items.find(item => item.id === itemId);
    if (!item) return;

    const lang = textarea.name.includes('-ru-') ? 'ru' : 'en';
    item.content[lang] = textarea.value;

    this.updateItemsData(items);
    this.updatePreview();
  }

  updateItemPosition(itemId, input) {
    const items = this.getGridItems();
    const item = items.find(item => item.id === itemId);
    if (!item) return;

    if (input.name.includes('grid-column')) {
      item.gridColumn = input.value || undefined;
    } else if (input.name.includes('grid-row')) {
      item.gridRow = input.value || undefined;
    }

    this.updateItemsData(items);
    this.updatePreview();
  }

  updateAddButton(currentCount) {
    const addButton = this.container.querySelector('.add-item-btn');
    const addSection = this.container.querySelector('.add-item-section');

    if (currentCount >= 12) {
      if (addSection) addSection.style.display = 'none';
    } else {
      if (addSection) addSection.style.display = '';
      if (addButton) {
        addButton.textContent = `+ Добавить элемент (${currentCount + 1}/12)`;
      }
    }
  }

  switchLanguage(itemId, lang) {
    const itemContainer = this.container.querySelector(`[data-item-id="${itemId}"]`);
    if (!itemContainer) return;

    // Переключаем активную вкладку
    const tabs = itemContainer.querySelectorAll('.lang-tab');
    tabs.forEach(tab => {
      tab.classList.toggle('active', tab.dataset.lang === lang);
    });

    // Переключаем контент
    const contents = itemContainer.querySelectorAll('.lang-content');
    contents.forEach(content => {
      content.style.display = content.dataset.lang === lang ? '' : 'none';
    });
  }

  // Обновление превью из данных EditorJS
  updatePreviewFromEditor(editorBlocks) {
    if (!window.EditorJSGridParser) {
      console.error('EditorJSGridParser не загружен');
      return;
    }

    // Преобразуем блоки EditorJS в элементы Grid
    const gridItems = window.EditorJSGridParser.parseBlocksForGrid(editorBlocks);

    // Обновляем данные элементов
    this.updateItemsData(gridItems);

    // Активируем превью
    this.setPreviewActive(true);

    // Обновляем отображение
    this.updatePreview();

    console.log(`Grid превью обновлено с ${gridItems.length} элементами`);
  }

  // Установка состояния превью
  setPreviewActive(active) {
    const hiddenInput = this.container.querySelector('input[name="gridSystemData"]');
    if (hiddenInput) {
      try {
        const data = JSON.parse(hiddenInput.value);
        data.previewActive = active;
        hiddenInput.value = JSON.stringify(data);
      } catch (e) {
        console.error('Ошибка обновления данных превью:', e);
      }
    }
  }

  // Получение текущих настроек для сохранения
  getSettings() {
    const enabled = this.container.querySelector('input[name="gridEnabled"]')?.checked || false;
    const mode = this.container.querySelector('input[name^="gridMode"]:checked')?.value || 'beginner';

    const settings = {
      enabled,
      mode,
      items: this.getGridItems(),
      previewActive: true
    };

    if (mode === 'beginner') {
      settings.beginnerSettings = this.getBeginnerSettings();
    } else {
      settings.proSettings = this.getProSettings();
    }

    return settings;
  }

  getBeginnerSettings() {
    return {
      columns: parseInt(this.container.querySelector('select[name="beginnerColumns"]')?.value || '2'),
      gap: {
        value: parseFloat(this.container.querySelector('input[name="beginnerGapValue"]')?.value || '1'),
        unit: this.container.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem'
      },
      itemAlignment: this.container.querySelector('select[name="beginnerItemAlignment"]')?.value || 'stretch',
      contentAlignment: this.container.querySelector('select[name="beginnerContentAlignment"]')?.value || 'start'
    };
  }

  getProSettings() {
    return {
      gridTemplateColumns: this.container.querySelector('input[name="proGridTemplateColumns"]')?.value || 'repeat(2, 1fr)',
      gridTemplateRows: this.container.querySelector('input[name="proGridTemplateRows"]')?.value || '',
      gap: {
        value: parseFloat(this.container.querySelector('input[name="proGapValue"]')?.value || '1'),
        unit: this.container.querySelector('select[name="proGapUnit"]')?.value || 'rem'
      },
      alignItems: this.container.querySelector('select[name="proAlignItems"]')?.value || 'stretch',
      justifyItems: this.container.querySelector('select[name="proJustifyItems"]')?.value || 'stretch'
    };
  }
}

// Экспортируем класс для использования
window.GridSystemManager = CompactGridSystemManager;
window.CompactGridSystemManager = CompactGridSystemManager;
