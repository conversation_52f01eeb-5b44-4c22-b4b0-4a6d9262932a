---
// Компонент вкладок для страницы товара
---
<div class="mt-10">
  <div class="flex border-b mb-4" role="tablist">
    <button id="tab-payment" class="tab-btn active px-4 py-2 font-semibold text-gray-700 border-b-2 border-primary focus:outline-none" role="tab" aria-selected="true" aria-controls="panel-payment" tabindex="0">Оплата</button>
    <button id="tab-delivery" class="tab-btn px-4 py-2 font-semibold text-gray-700 border-b-2 border-transparent focus:outline-none" role="tab" aria-selected="false" aria-controls="panel-delivery" tabindex="-1">Доставка</button>
  </div>
  <div id="panel-payment" class="tab-panel" role="tabpanel" aria-labelledby="tab-payment">
    <p>Здесь будет информация об оплате (заглушка).</p>
  </div>
  <div id="panel-delivery" class="tab-panel hidden" role="tabpanel" aria-labelledby="tab-delivery">
    <p>Здесь будет информация о доставке (заглушка).</p>
  </div>
</div>

<style>
  .tab-btn {
    transition: background 0.2s, border-color 0.2s, color 0.2s;
    background: transparent;
    border-bottom-width: 2px;
    border-bottom-style: solid;
    border-color: transparent;
    color: #222;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    /* Высота не меняем, только ширину за счет паддингов */
  }
  .tab-btn.active {
    color: #baa385;
    border-color: #baa385;
    background: #f9f7f3;
  }
  .tab-panel {
    padding: 1.5rem 0 0 0;
  }
  .tab-panel.hidden {
    display: none;
  }
</style>

<script is:inline>
  document.addEventListener('DOMContentLoaded', function () {
    const tabPayment = document.getElementById('tab-payment');
    const tabDelivery = document.getElementById('tab-delivery');
    const panelPayment = document.getElementById('panel-payment');
    const panelDelivery = document.getElementById('panel-delivery');

    function setActiveTab(activeBtn, inactiveBtn, showPanel, hidePanel) {
      activeBtn.classList.add('active');
      inactiveBtn.classList.remove('active');
      activeBtn.setAttribute('aria-selected', 'true');
      inactiveBtn.setAttribute('aria-selected', 'false');
      activeBtn.setAttribute('tabindex', '0');
      inactiveBtn.setAttribute('tabindex', '-1');
      showPanel.classList.remove('hidden');
      hidePanel.classList.add('hidden');
    }

    if (tabPayment && tabDelivery && panelPayment && panelDelivery) {
      tabPayment.addEventListener('click', function () {
        setActiveTab(tabPayment, tabDelivery, panelPayment, panelDelivery);
      });
      tabDelivery.addEventListener('click', function () {
        setActiveTab(tabDelivery, tabPayment, panelDelivery, panelPayment);
      });
    }
  });
</script> 