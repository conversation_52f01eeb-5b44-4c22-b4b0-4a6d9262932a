// Пример использования парсера Editor.js контента

import editorJSParser from '../src/settings/utils/editorjs-parser.js';

// Пример 1: Простой JSON от Editor.js
const simpleEditorData = {
  "time": 1753191566800,
  "blocks": [
    {
      "id": "bO3RzZiVt7",
      "type": "header",
      "data": {
        "text": "Заголовок статьи",
        "level": 1
      }
    },
    {
      "id": "aI8dadh_Hv",
      "type": "paragraph",
      "data": {
        "text": "Это простой параграф текста с <b>жирным</b> выделением."
      }
    },
    {
      "id": "VFtIk_CRHR",
      "type": "list",
      "data": {
        "style": "unordered",
        "items": [
          { "content": "Первый пункт списка", "meta": {}, "items": [] },
          { "content": "Второй пункт списка", "meta": {}, "items": [] },
          { "content": "Третий пункт списка", "meta": {}, "items": [] }
        ]
      }
    }
  ],
  "version": "2.31.0-rc.7"
};

// Преобразование в HTML
const htmlOutput = editorJSParser.parseToHTML(JSON.stringify(simpleEditorData));
console.log('HTML результат:', htmlOutput);

// Пример 2: Работа с блоком контента
const contentBlock = {
  id: "test-block",
  type: "text",
  enabled: true,
  order: 1,
  content: {
    ru: JSON.stringify(simpleEditorData),
    en: JSON.stringify({
      "time": 1753191566800,
      "blocks": [
        {
          "id": "en_header",
          "type": "header",
          "data": {
            "text": "Article Title",
            "level": 1
          }
        },
        {
          "id": "en_paragraph",
          "type": "paragraph",
          "data": {
            "text": "This is a simple paragraph with <b>bold</b> text."
          }
        }
      ],
      "version": "2.31.0-rc.7"
    })
  }
};

// Парсинг блока для русского языка
const ruContent = editorJSParser.parseBlockContent(contentBlock, 'ru');
console.log('Русский контент:', ruContent);

// Парсинг блока для английского языка
const enContent = editorJSParser.parseBlockContent(contentBlock, 'en');
console.log('Английский контент:', enContent);

// Пример 3: Обработка сложных блоков
const complexEditorData = {
  "time": 1753288396106,
  "blocks": [
    {
      "id": "image_block",
      "type": "image",
      "data": {
        "caption": "Пример изображения",
        "withBorder": true,
        "withBackground": false,
        "stretched": true,
        "file": {
          "url": "/uploads/example-image.jpg",
          "name": "example.jpg",
          "size": 507083
        }
      }
    },
    {
      "id": "quote_block",
      "type": "quote",
      "data": {
        "text": "Это пример цитаты в тексте статьи.",
        "caption": "Автор цитаты"
      }
    },
    {
      "id": "code_block",
      "type": "code",
      "data": {
        "code": "function example() {\n  console.log('Hello, World!');\n}"
      }
    },
    {
      "id": "table_block",
      "type": "table",
      "data": {
        "content": [
          ["Заголовок 1", "Заголовок 2", "Заголовок 3"],
          ["Ячейка 1", "Ячейка 2", "Ячейка 3"],
          ["Ячейка 4", "Ячейка 5", "Ячейка 6"]
        ]
      }
    },
    {
      "id": "button_block",
      "type": "button",
      "data": {
        "text": "Нажми меня",
        "link": "https://example.com"
      }
    }
  ],
  "version": "2.31.0-rc.7"
};

const complexHtml = editorJSParser.parseToHTML(JSON.stringify(complexEditorData));
console.log('Сложный HTML:', complexHtml);

// Пример 4: Обработка ошибок
const invalidData = '{"invalid": "json"}';
const errorResult = editorJSParser.parseToHTML(invalidData);
console.log('Результат с ошибкой:', errorResult);

// Пример 5: Пустые данные
const emptyResult = editorJSParser.parseToHTML('');
console.log('Пустой результат:', emptyResult);

// Пример 6: Использование в Astro компоненте
/*
---
import editorJSParser from '../utils/editorjs-parser.js';

// Получаем данные страницы
const pageData = await getPageData();
const blocks = pageData.blocks || [];

// Фильтруем активные блоки
const activeBlocks = blocks.filter(block => block.enabled);
---

{activeBlocks.map(block => (
  <section class="content-block" data-block-type={block.type}>
    <Fragment set:html={editorJSParser.parseBlockContent(block, 'ru')} />
  </section>
))}
*/

export {
  simpleEditorData,
  contentBlock,
  complexEditorData,
  htmlOutput,
  ruContent,
  enContent,
  complexHtml
};
