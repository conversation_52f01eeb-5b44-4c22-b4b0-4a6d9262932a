---
// ShareBlock.astro — универсальный компонент для шаринга
// Параметры: url (по умолчанию window.location.href), title, description (опционально)
const { url = '', title = '', description = '' } = Astro.props;
const shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
const shareTitle = title || '';
const shareDesc = description || '';

// Формируем ссылки для соцсетей
const encodedUrl = encodeURIComponent(shareUrl);
const encodedTitle = encodeURIComponent(shareTitle);
const encodedDesc = encodeURIComponent(shareDesc);

const shareLinks = [
  {
    name: 'Telegram',
    icon: 'mdi:telegram',
    url: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`
  },
  {
    name: 'WhatsApp',
    icon: 'mdi:whatsapp',
    url: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`
  },
  {
    name: 'Viber',
    icon: 'simple-icons:viber',
    url: `https://viber://forward?text=${encodedTitle}%20${encodedUrl}`
  },
  {
    name: 'VK',
    icon: 'mdi:vk',
    url: `https://vk.com/share.php?url=${encodedUrl}&title=${encodedTitle}`
  },
  {
    name: 'Facebook',
    icon: 'mdi:facebook',
    url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
  },
  {
    name: 'Email',
    icon: 'mdi:email-outline',
    url: `mailto:?subject=${encodedTitle}&body=${encodedDesc}%0A${encodedUrl}`
  }
];
---

<div class="flex items-center my-10">
  <span class="text-base text-gray-500 mr-4 whitespace-nowrap">Поделиться:</span>
  <div class="flex gap-3">
    {shareLinks.map(link => (
      <a
        href={link.url}
        target="_blank"
        rel="noopener noreferrer"
        aria-label={`Поделиться в ${link.name}`}
        class="text-gray-500 hover:text-primary transition-colors text-2xl"
      >
        <span class="iconify align-middle" data-icon={link.icon}></span>
      </a>
    ))}
    <!-- Кнопка копирования ссылки -->
    <div class="relative">
      <button
        id="share-copy-btn"
        type="button"
        aria-label="Скопировать ссылку"
        class="text-gray-500 hover:text-primary transition-colors text-xl focus:outline-none"
        data-share-url={shareUrl}
      >
        <span class="iconify align-bottom" data-icon="mdi:content-copy"></span>
      </button>
      <span id="share-copy-popup" class="absolute left-1/2 -translate-x-1/2 top-full mt-1 px-2 py-1 rounded bg-gray-800 text-white text-xs opacity-0 pointer-events-none transition-opacity duration-200 whitespace-nowrap z-10">Скопировано</span>
    </div>
  </div>
</div>

<script>
  if (typeof window !== 'undefined') {
    const btn = document.getElementById('share-copy-btn');
    const popup = document.getElementById('share-copy-popup');
    if (btn && window.navigator && window.navigator.clipboard) {
      btn.addEventListener('click', () => {
        const url = btn.getAttribute('data-share-url') || window.location.href;
        window.navigator.clipboard.writeText(url);
        if (popup) {
          popup.style.opacity = '1';
          setTimeout(() => {
            popup.style.opacity = '0';
          }, 1500);
        }
      });
    }
  }
</script> 