---
import editorJ<PERSON>arser from '../../utils/editorjs-parser.js';

export interface Props {
  block: {
    id: string;
    type: string;
    enabled: boolean;
    order: number;
    content: {
      [lang: string]: any;
    };
    name?: string;
    displaySettings?: {
      maxWidth?: {
        value: number;
        unit: 'px' | '%';
      };
      alignment?: 'left' | 'center' | 'right';
      padding?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
        unit: 'px' | '%';
      };
      margin?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
        unit: 'px' | '%';
      };
      gridColumns?: number;
      elementGap?: {
        value: number;
        unit: 'px' | 'rem';
      };
      gridSystem?: {
        enabled: boolean;
        mode: 'simple';
        // Основные настройки Grid
        columns: number;
        gap: { value: number; unit: string };
        itemAlignment: string;
        contentAlignment: string;
        // Дополнительные настройки
        autoRows?: string;
        justifyItems?: string;
        alignContent?: string;
        justifyContent?: string;
        // Адаптивные настройки
        responsive?: {
          desktop: {
            columns: number;
            gap?: { value: number; unit: string };
          };
          tablet: {
            columns: number;
            gap?: { value: number; unit: string };
          };
          mobile: {
            columns: number;
            gap?: { value: number; unit: string };
          };
        };
        // Элементы Grid
        items?: any[];
        previewActive?: boolean;
      };
    };
  };
  lang?: string;
  gridEnabled?: boolean;
  gridSettings?: {
    enabled: boolean;
    mode: 'simple';
    // Основные настройки Grid
    columns: number;
    gap: { value: number; unit: string };
    itemAlignment: string;
    contentAlignment: string;
    // Дополнительные настройки
    autoRows?: string;
    justifyItems?: string;
    alignContent?: string;
    justifyContent?: string;
    // Адаптивные настройки
    responsive?: {
      desktop: {
        columns: number;
        gap?: { value: number; unit: string };
      };
      tablet: {
        columns: number;
        gap?: { value: number; unit: string };
      };
      mobile: {
        columns: number;
        gap?: { value: number; unit: string };
      };
    };
    // Элементы Grid
    items?: any[];
    previewActive?: boolean;
  };
}

const { block, lang = 'ru', gridEnabled = false, gridSettings } = Astro.props;

/**
 * Рендерит специальные типы блоков, которые требуют особой обработки
 */
function renderSpecialBlock(block, lang, gridEnabled = false) {
  // Проверяем, нужно ли применить grid-обертку
  const hasGrid = block.displaySettings?.gridColumns && block.displaySettings.gridColumns > 1;

  let content = '';
  switch (block.type) {
    case 'slider':
      content = renderSliderBlock(block, lang, gridEnabled, gridSettings);
      break;
    case 'hero':
      content = renderHeroBlock(block, lang, gridEnabled, gridSettings);
      break;
    case 'gallery':
      content = renderGalleryBlock(block, lang, gridEnabled, gridSettings);
      break;
    case 'video':
      content = renderVideoBlock(block, lang, gridEnabled, gridSettings);
      break;
    case 'contacts':
      content = renderContactsBlock(block, lang, gridEnabled, gridSettings);
      break;
    case 'map':
      content = renderMapBlock(block, lang, gridEnabled, gridSettings);
      break;
    default:
      // Для всех остальных типов используем стандартный парсер Editor.js
      content = editorJSParser.parseBlockContent(block, lang, gridEnabled, gridSettings);
      break;
  }

  // Если нужен grid, оборачиваем контент в специальную структуру
  if (hasGrid && content) {
    return wrapContentForGrid(content, block.displaySettings);
  }

  return content;
}

/**
 * Оборачивает контент для grid-отображения
 */
function wrapContentForGrid(content, displaySettings) {
  // Парсим HTML контент и разбиваем на отдельные элементы
  const tempDiv = `<div>${content}</div>`;

  // Для grid-отображения нужно обернуть каждый элемент в отдельный контейнер
  // Это позволит CSS grid правильно распределить элементы
  return `<div class="grid-content-wrapper">${content}</div>`;
}

/**
 * Рендерит блок слайдера
 */
function renderSliderBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
  }

  // Если это старый формат с массивом слайдов
  if (content.slides && Array.isArray(content.slides)) {
    const slides = content.slides.map((slide, index) =>
      `<div class="slide" data-slide="${index}">
        <div class="slide-content p-8 bg-gray-100 rounded-lg">
          <p class="text-lg text-gray-700">${slide}</p>
        </div>
      </div>`
    ).join('');

    return `<div class="slider-container">
      <div class="slides">${slides}</div>
      <div class="slider-controls mt-4 text-center">
        <button class="prev-slide bg-blue-600 text-white px-4 py-2 rounded mr-2">Предыдущий</button>
        <button class="next-slide bg-blue-600 text-white px-4 py-2 rounded">Следующий</button>
      </div>
    </div>`;
  }

  return '';
}

/**
 * Рендерит hero блок
 */
function renderHeroBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его и оборачиваем в hero контейнер
  if (typeof content === 'string') {
    const parsedContent = editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
    return `<div class="hero-content bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 px-8 rounded-lg">
      ${parsedContent}
    </div>`;
  }

  return '';
}

/**
 * Рендерит блок галереи
 */
function renderGalleryBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его и оборачиваем в галерею
  if (typeof content === 'string') {
    const parsedContent = editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
    return `<div class="gallery-content">
      ${parsedContent}
    </div>`;
  }

  return '';
}

/**
 * Рендерит блок видео
 */
function renderVideoBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
  }

  return '';
}

/**
 * Рендерит блок контактов
 */
function renderContactsBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
  }

  return '';
}

/**
 * Рендерит блок карты
 */
function renderMapBlock(block, lang, gridEnabled = false, gridSettings) {
  const content = block.content?.[lang];
  if (!content) return '';

  // Если это JSON от Editor.js, парсим его
  if (typeof content === 'string') {
    return editorJSParser.parseToHTML(content, gridEnabled, gridSettings);
  }

  return '';
}

// Получаем HTML для блока
let blockHTML = renderSpecialBlock(block, lang, gridEnabled);


---

{blockHTML && (
  <div
    class="block-renderer"
    data-block-id={block.id}
    data-block-type={block.type}
    set:html={blockHTML}
  />
)}

<style>
  .block-renderer {
    @apply w-full;
  }

  /* Стили для слайдера */
  .slider-container {
    @apply relative;
  }

  .slides {
    @apply space-y-4;
  }

  .slide {
    @apply w-full;
  }

  .slide-content {
    @apply transition-all duration-300;
  }

  .slider-controls button {
    @apply transition-colors duration-200 hover:bg-blue-700;
  }

  /* Стили для hero блока */
  .hero-content {
    @apply text-center;
  }

  .hero-content :global(h1),
  .hero-content :global(h2),
  .hero-content :global(h3) {
    @apply text-white;
  }

  .hero-content :global(p) {
    @apply text-blue-100;
  }

  /* Стили для галереи */
  .gallery-content {
    @apply grid gap-4;
  }

  .gallery-content :global(img) {
    @apply rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200;
  }

  /* Адаптивная сетка для галереи */
  @media (min-width: 640px) {
    .gallery-content {
      @apply grid-cols-2;
    }
  }

  @media (min-width: 1024px) {
    .gallery-content {
      @apply grid-cols-3;
    }
  }
</style>
