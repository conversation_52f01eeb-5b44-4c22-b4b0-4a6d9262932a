---
// Компонент для отображения списка товаров в корзине на странице оформления заказа
---

<div class="cart-items-list">
  <div class="cart-header">
    <h2 class="cart-title">Ваш заказ</h2>
  </div>

  <!-- Контейнер для товаров из корзины -->
  <div id="cart-order-items" class="cart-order-items">
    <div class="empty-cart-message">
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 2.5M7 13l2.5 2.5m0 0L17 21H7l2.5-2.5z" />
        </svg>
        <p class="text-gray-500 text-lg">Корзина пуста</p>
        <p class="text-gray-400 text-sm mt-2">Добавьте товары из каталога</p>
        <a href="/products" class="inline-block mt-4 bg-primary text-white px-6 py-2 hover:bg-primary-dark transition-colors">
          Перейти в каталог
        </a>
      </div>
    </div>
  </div>

  <!-- Примечание -->
  <div id="cart-note" class="cart-note hidden">
    <div class="note-content">
      <span class="note-asterisk">*</span>
      <span class="note-text">Если необходимо рассчитать необходимое кол-во конкретного товара под ваши условия, форма ниже поможет это сделать, просто выделите конкретный товар</span>
    </div>
  </div>

  <!-- Итоговая сумма -->
  <div id="cart-total-section" class="cart-total-section hidden">
    <div class="total-row">
      <a href="/products" class="add-more-products-btn">Добавить еще товары</a>
      <div class="total-info">
        <span class="total-label">Сумма заказа:</span>
        <span id="cart-total-amount" class="total-amount">итоговая сумма</span>
      </div>
    </div>
  </div>
</div>

<style>
  .cart-items-list {
    background: white;
    border: 1px solid #d1d5db;
    padding: 20px;
    height: fit-content;
  }

  .cart-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
  }

  .cart-order-items {
    min-height: 200px;
  }

  .cart-items-container {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  /* Стили теперь применяются через inline стили в JavaScript */

  .cart-note {
    margin: 20px 0;
    padding: 12px 0;
    border-top: 1px solid #e5e7eb;
  }

  .note-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 12px;
    line-height: 1.4;
    color: #374151;
  }

  .note-asterisk {
    color: #1f2937;
    font-weight: bold;
    margin-top: 1px;
  }

  .note-text {
    flex: 1;
  }

  .cart-total-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 16px;
    margin-top: 16px;
  }

  .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .add-more-products-btn {
    background: transparent;
    color: #baa385;
    padding: 8px 16px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #baa385;
    transition: all 0.2s ease;
    white-space: nowrap;
    text-transform: uppercase;
  }

  .add-more-products-btn:hover {
    background: #baa385;
    color: white;
  }

  .total-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
  }

  .total-label {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    white-space: nowrap;
  }

  .total-amount {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    white-space: nowrap;
  }

  @media (max-width: 768px) {
    .cart-items-list {
      padding: 16px;
    }

    .total-row {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .add-more-products-btn {
      text-align: center;
      order: 2;
    }

    .total-info {
      justify-content: space-between;
      order: 1;
    }

    /* Мобильная адаптация применяется через JavaScript */
  }
</style>

<script>
  // Функция для преобразования единиц измерения
  function getUnitName(unit) {
    if (!unit) return '';

    const unitNames = {
      // Штучные
      'piece': 'шт',
      'pack': 'упаковка',
      'set': 'набор',
      'шт': 'шт',

      // Размеры
      'm': 'м',
      'cm': 'см',
      'mm': 'мм',
      'm2': 'м²',
      'm3': 'м³',
      'м': 'м',
      'м2': 'м²',
      'м3': 'м³',

      // Вес
      'kg': 'кг',
      'g': 'г',
      'lb': 'фунт',
      'oz': 'унция',
      'кг': 'кг',
      'г': 'г',

      // Объем
      'L': 'л',
      'mL': 'мл',
      'gal': 'галлон',
      'л': 'л',
      'мл': 'мл',

      // Услуги
      'hour': 'час',
      'day': 'день',
      'month': 'месяц',
      'project': 'проект'
    };

    return unitNames[unit] || unit;
  }

  // Функция для форматирования валют
  function formatCurrency(currency) {
    const currencyMap = {
      'BYN': 'BYN',
      'USD': '$',
      'EUR': '€',
      'RUB': '₽'
    };
    return currencyMap[currency] || currency;
  }

  // Функция для отображения товаров из корзины
  function displayCartItems() {
    const cartOrderItems = document.getElementById('cart-order-items');
    const cartTotalSection = document.getElementById('cart-total-section');
    const cartTotalAmount = document.getElementById('cart-total-amount');
    
    if (!cartOrderItems || !cartTotalSection || !cartTotalAmount) return;

    // Получаем товары из localStorage
    const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');
    
    if (cart.length === 0) {
      cartOrderItems.innerHTML = `
        <div class="empty-cart-message">
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 2.5M7 13l2.5 2.5m0 0L17 21H7l2.5-2.5z" />
            </svg>
            <p class="text-gray-500 text-lg">Корзина пуста</p>
            <p class="text-gray-400 text-sm mt-2">Добавьте товары из каталога</p>
            <a href="/products" class="inline-block mt-4 bg-primary text-white px-6 py-2 hover:bg-primary-dark transition-colors">
              Перейти в каталог
            </a>
          </div>
        </div>
      `;
      cartTotalSection.classList.add('hidden');
      return;
    }

    // Отображаем товары
    const cartItemsHTML = cart.map((item, index) => {
      const itemTotal = item.price * item.quantity;

      return `
        <div class="cart-item" data-index="${index}" style="display: flex; align-items: center; gap: 16px; padding: 16px 0; border-bottom: 1px solid #e5e7eb;">
          <input type="checkbox" class="cart-item-checkbox" style="width: 18px; height: 18px; border: 1px solid #6b7280; background: white; cursor: pointer; flex-shrink: 0; border-radius: 0;" />
          <a href="${item.productUrl || '#'}" class="cart-item-image" style="width: 80px; height: 80px; object-fit: cover; background: #f3f4f6; border: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: center; color: #6b7280; font-size: 11px; text-align: center; flex-shrink: 0; text-decoration: none; transition: opacity 0.2s;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">
            ${item.image ? `<img src="${item.image}" alt="${item.name}" style="width: 100%; height: 100%; object-fit: cover;" />` : 'фото товара'}
          </a>
          <div class="cart-item-details" style="display: flex; flex-direction: column; gap: 4px; flex: 1; min-width: 0;">
            <a href="${item.productUrl || '#'}" class="cart-item-name" style="font-weight: 600; color: #1f2937; font-size: 14px; line-height: 1.4; text-decoration: none; transition: color 0.2s;" onmouseover="this.style.color='#baa385'" onmouseout="this.style.color='#1f2937'">${item.name}</a>
            <div class="cart-item-price" style="color: #6b7280; font-size: 13px;">${item.price} ${item.currency}${item.unit ? ` / ${getUnitName(item.unit)}` : ''}</div>
          </div>
          <div class="cart-item-quantity" style="display: flex; align-items: center; gap: 8px; flex-shrink: 0;">
            <button class="quantity-btn" onclick="updateQuantity(${index}, -1)" style="background: #f3f4f6; border: 1px solid #d1d5db; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px; font-weight: bold; color: #374151; transition: background-color 0.2s; border-radius: 0;" onmouseover="this.style.background='#e5e7eb'" onmouseout="this.style.background='#f3f4f6'">−</button>
            <div class="quantity-display-wrapper" style="min-width: 50px; height: 32px; position: relative; background: white; border: 1px solid #d1d5db; border-radius: 0;">
              <span class="quantity-display" onclick="editQuantity(${index}, this)" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 600; cursor: pointer; color: #374151;">${item.quantity}</span>
              <input type="number" min="1" max="999" value="${item.quantity}" class="quantity-input" onblur="saveQuantity(${index}, this)" onkeydown="handleQuantityKeydown(${index}, this, event)" style="width: 100%; height: 100%; text-align: center; font-size: 14px; font-weight: 600; background: white; border: none; outline: none; position: absolute; top: 0; left: 0; display: none; border-radius: 0; -moz-appearance: textfield;" />
            </div>
            <button class="quantity-btn" onclick="updateQuantity(${index}, 1)" style="background: #f3f4f6; border: 1px solid #d1d5db; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px; font-weight: bold; color: #374151; transition: background-color 0.2s; border-radius: 0;" onmouseover="this.style.background='#e5e7eb'" onmouseout="this.style.background='#f3f4f6'">+</button>
          </div>
          <div class="cart-item-total" style="font-weight: 600; color: #1f2937; font-size: 14px; text-align: right; min-width: 100px; flex-shrink: 0;">${itemTotal.toFixed(2)} ${item.currency}</div>
          <button class="remove-item-btn" onclick="removeItem(${index})" title="Удалить товар" style="background: #f3f4f6; border: 1px solid #d1d5db; color: #ef4444; cursor: pointer; padding: 8px; display: flex; align-items: center; justify-content: center; transition: all 0.2s; flex-shrink: 0; width: 32px; height: 32px; border-radius: 0;" onmouseover="this.style.background='#ef4444'; this.style.color='white'; this.style.borderColor='#ef4444';" onmouseout="this.style.background='#f3f4f6'; this.style.color='#ef4444'; this.style.borderColor='#d1d5db';">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" style="width: 16px; height: 16px;">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      `;
    }).join('');

    cartOrderItems.innerHTML = `<div class="cart-items-container" style="display: flex; flex-direction: column; gap: 0;">${cartItemsHTML}</div>`;

    // Убираем нижнюю границу у последнего элемента
    const lastCartItem = cartOrderItems.querySelector('.cart-item:last-child');
    if (lastCartItem) {
      lastCartItem.style.borderBottom = 'none';
    }

    // Мобильная адаптация
    if (window.innerWidth <= 768) {
      const cartItems = cartOrderItems.querySelectorAll('.cart-item');
      cartItems.forEach(item => {
        item.style.flexWrap = 'wrap';
        item.style.gap = '12px';
        item.style.padding = '12px 0';

        const image = item.querySelector('.cart-item-image');
        if (image) {
          image.style.width = '60px';
          image.style.height = '60px';
        }

        const details = item.querySelector('.cart-item-details');
        if (details) {
          details.style.flex = '1 1 100%';
          details.style.marginBottom = '8px';
        }
      });
    }

    // Группируем товары по валютам и считаем сумму для каждой валюты
    const currencyTotals = {};
    cart.forEach(item => {
      const currency = item.currency || 'BYN';
      const itemTotal = parseFloat(item.price) * item.quantity;
      currencyTotals[currency] = (currencyTotals[currency] || 0) + itemTotal;
    });

    // Формируем строку с суммами по валютам
    const totalStrings = Object.entries(currencyTotals).map(([currency, total]) =>
      `${total.toFixed(2)} ${formatCurrency(currency)}`
    );

    // Показываем итоговую сумму и примечание
    cartTotalAmount.textContent = totalStrings.join(' + ');
    cartTotalSection.classList.remove('hidden');

    // Показываем примечание
    const cartNote = document.getElementById('cart-note');
    if (cartNote) {
      cartNote.classList.remove('hidden');
    }
  }

  // Функции для управления количеством товаров
  window.updateQuantity = function(index, change) {
    const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');
    if (cart[index]) {
      cart[index].quantity = Math.max(1, cart[index].quantity + change);
      localStorage.setItem('shopping-cart', JSON.stringify(cart));
      displayCartItems();
      
      // Обновляем счетчик в корзине, если есть
      if (window.cartManager) {
        window.cartManager.updateCartDisplay();
      }
    }
  };

  window.setQuantity = function(index, value) {
    const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');
    const quantity = Math.max(1, parseInt(value) || 1);
    if (cart[index]) {
      cart[index].quantity = quantity;
      localStorage.setItem('shopping-cart', JSON.stringify(cart));
      displayCartItems();
      
      // Обновляем счетчик в корзине, если есть
      if (window.cartManager) {
        window.cartManager.updateCartDisplay();
      }
    }
  };

  window.removeItem = function(index) {
    const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');
    cart.splice(index, 1);
    localStorage.setItem('shopping-cart', JSON.stringify(cart));
    displayCartItems();

    // Обновляем счетчик в корзине, если есть
    if (window.cartManager) {
      window.cartManager.updateCartDisplay();
    }
  };

  // Функции для редактирования количества
  window.editQuantity = function(index, spanElement) {
    const wrapper = spanElement.parentElement;
    const input = wrapper.querySelector('.quantity-input');

    // Скрываем span и показываем input
    spanElement.style.display = 'none';
    input.style.display = 'flex';
    input.focus();
    input.select();
  };

  window.saveQuantity = function(index, inputElement) {
    const wrapper = inputElement.parentElement;
    const span = wrapper.querySelector('.quantity-display');
    const newQuantity = Math.max(1, parseInt(inputElement.value) || 1);

    // Обновляем количество
    window.setQuantity(index, newQuantity);

    // Возвращаем отображение span
    inputElement.style.display = 'none';
    span.style.display = 'flex';
  };

  window.handleQuantityKeydown = function(index, inputElement, event) {
    if (event.key === 'Enter') {
      inputElement.blur(); // Это вызовет saveQuantity
    } else if (event.key === 'Escape') {
      const wrapper = inputElement.parentElement;
      const span = wrapper.querySelector('.quantity-display');
      const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');

      // Отменяем изменения и возвращаем исходное значение
      inputElement.value = cart[index].quantity;
      inputElement.style.display = 'none';
      span.style.display = 'flex';
    }
  };

  // Инициализация при загрузке страницы
  document.addEventListener('DOMContentLoaded', displayCartItems);

  // === ЭФФЕКТ: quantity-input очищается при фокусе, восстанавливается при blur ===
  document.addEventListener('DOMContentLoaded', function() {
    const cartQtyInputs = document.querySelectorAll('.quantity-input');
    cartQtyInputs.forEach(function(input) {
      input.removeAttribute('placeholder');
      input.addEventListener('focus', function(e) {
        const el = e.target as HTMLInputElement;
        if (el && !el.hasAttribute('data-prev')) {
          el.setAttribute('data-prev', el.value);
        }
        if (el) el.value = '';
      });
      input.addEventListener('blur', function(e) {
        const el = e.target as HTMLInputElement;
        if (el && el.value === '' && el.hasAttribute('data-prev')) {
          el.value = el.getAttribute('data-prev') || '';
        }
        if (el) el.removeAttribute('data-prev');
      });
    });
  });
</script>
