---
import AdminLayout from '../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../utils/auth';
import Card from '../../components/ui/Card.astro';
import CardHeader from '../../components/ui/CardHeader.astro';
import CardTitle from '../../components/ui/CardTitle.astro';
import CardDescription from '../../components/ui/CardDescription.astro';
import CardContent from '../../components/ui/CardContent.astro';
import Button from '../../components/ui/Button.astro';
import Badge from '../../components/ui/Badge.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}
---

<AdminLayout title="Заявки | LuxBeton Admin">
  <div class="space-y-6">
    <!-- Заголовок страницы -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Заявки</h1>
        <p class="text-gray-600 mt-1">Управление заявками на товары и запросами на звонки</p>
      </div>
    </div>

    <!-- Фильтры -->
    <Card>
      <CardContent>
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex gap-2">
            <label class="text-sm font-medium text-gray-700">Тип:</label>
            <select id="typeFilter" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
              <option value="">Все</option>
              <option value="product">Заказы товаров</option>
              <option value="call">Запросы на звонок</option>
            </select>
          </div>
          <div class="flex gap-2">
            <label class="text-sm font-medium text-gray-700">Статус:</label>
            <select id="statusFilter" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
              <option value="">Все</option>
              <option value="new">Новые</option>
              <option value="processing">В обработке</option>
              <option value="completed">Завершенные</option>
              <option value="cancelled">Отмененные</option>
            </select>
          </div>
          <Button id="refreshBtn" variant="outline" size="sm">
            Обновить
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Статистика -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
            <div class="text-sm text-gray-600">Всего заявок</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600" id="newCount">0</div>
            <div class="text-sm text-gray-600">Новые</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent>
          <div class="text-center">
            <div class="text-2xl font-bold text-yellow-600" id="processingCount">0</div>
            <div class="text-sm text-gray-600">В обработке</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-600" id="completedCount">0</div>
            <div class="text-sm text-gray-600">Завершенные</div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Список заявок -->
    <Card>
      <CardHeader>
        <CardTitle>Список заявок</CardTitle>
        <CardDescription>Все заявки отсортированы по дате создания</CardDescription>
      </CardHeader>
      <CardContent>
        <div id="loadingIndicator" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-2 text-gray-600">Загрузка заявок...</p>
        </div>
        
        <div id="ordersContainer" class="hidden space-y-4">
          <!-- Заявки будут загружены динамически -->
        </div>
        
        <div id="emptyState" class="hidden text-center py-8">
          <p class="text-gray-500">Заявки не найдены</p>
        </div>
      </CardContent>
    </Card>
  </div>

  <script>
    let allOrders = [];
    let filteredOrders = [];

    // Элементы DOM
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const refreshBtn = document.getElementById('refreshBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const ordersContainer = document.getElementById('ordersContainer');
    const emptyState = document.getElementById('emptyState');

    // Счетчики
    const totalCount = document.getElementById('totalCount');
    const newCount = document.getElementById('newCount');
    const processingCount = document.getElementById('processingCount');
    const completedCount = document.getElementById('completedCount');

    // Загрузка заявок
    async function loadOrders() {
      try {
        showLoading();
        
        const response = await fetch('/api/admin/orders');
        const result = await response.json();
        
        if (result.success) {
          allOrders = result.orders;
          applyFilters();
          updateStatistics();
        } else {
          throw new Error(result.error || 'Ошибка при загрузке заявок');
        }
      } catch (error) {
        console.error('Ошибка при загрузке заявок:', error);
        showError('Ошибка при загрузке заявок: ' + error.message);
      }
    }

    // Применение фильтров
    function applyFilters() {
      const typeValue = typeFilter.value;
      const statusValue = statusFilter.value;
      
      filteredOrders = allOrders.filter(order => {
        const typeMatch = !typeValue || order.type === typeValue;
        const statusMatch = !statusValue || order.status === statusValue;
        return typeMatch && statusMatch;
      });
      
      renderOrders();
    }

    // Отображение заявок
    function renderOrders() {
      hideLoading();
      
      if (filteredOrders.length === 0) {
        showEmptyState();
        return;
      }
      
      showOrdersContainer();
      
      ordersContainer.innerHTML = filteredOrders.map(order => {
        return createOrderCard(order);
      }).join('');
      
      // Добавляем обработчики событий для кнопок
      addOrderEventListeners();
    }

    // Создание карточки заявки
    function createOrderCard(order) {
      const statusBadge = getStatusBadge(order.status);
      const typeBadge = getTypeBadge(order.type);
      const createdAt = new Date(order.createdAt).toLocaleString('ru-RU');
      
      let content = '';
      if (order.type === 'product') {
        const itemsCount = order.items ? order.items.length : 0;
        const totalAmount = order.totalAmount || 0;
        content = `
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <p><strong>Клиент:</strong> ${order.clientName}</p>
              <p><strong>Телефон:</strong> ${order.phone}</p>
              ${order.companyName ? `<p><strong>Компания:</strong> ${order.companyName}</p>` : ''}
              <p><strong>Тип клиента:</strong> ${order.clientType === 'individual' ? 'Физическое лицо' : 'Юридическое лицо'}</p>
            </div>
            <div>
              <p><strong>Товаров:</strong> ${itemsCount}</p>
              <p><strong>Сумма:</strong> ${totalAmount.toFixed(2)} BYN</p>
              ${order.paymentMethod ? `<p><strong>Оплата:</strong> ${order.paymentMethod}</p>` : ''}
              ${order.deliveryMethod ? `<p><strong>Доставка:</strong> ${order.deliveryMethod}</p>` : ''}
            </div>
          </div>
          ${order.comment ? `<div class="mt-4"><p><strong>Комментарий:</strong> ${order.comment}</p></div>` : ''}
        `;
      } else if (order.type === 'quick_order') {
        // Для быстрых заявок
        const product = order.product || {};
        const quantity = order.quantity || 1;
        const price = typeof product.price === 'number' ? product.price : 0;
        const currency = product.currency || 'BYN';
        const sum = price * quantity;
        content = `
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <p><strong>Товар:</strong> ${product.id || '-'} (${quantity})</p>
            </div>
            <div>
              <p><strong>Сумма заказа:</strong> ${sum.toFixed(2)} ${currency}</p>
            </div>
          </div>
          ${order.comment ? `<div class="mt-4"><p><strong>Комментарий:</strong> ${order.comment}</p></div>` : ''}
        `;
      } else {
        content = `
          <div class="mt-4">
            <p><strong>Имя:</strong> ${order.name}</p>
            <p><strong>Телефон:</strong> ${order.phone}</p>
            ${order.message ? `<p><strong>Сообщение:</strong> ${order.message}</p>` : ''}
          </div>
        `;
      }
      
      return `
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <h3 class="font-semibold text-lg">${order.id}</h3>
                ${typeBadge}
                ${statusBadge}
              </div>
              <p class="text-sm text-gray-600">Создано: ${createdAt}</p>
              ${content}
            </div>
            <div class="flex gap-2 ml-4">
              <select class="status-select px-2 py-1 border border-gray-300 rounded text-sm" data-order-id="${order.id}" data-order-type="${order.type}">
                <option value="new" ${order.status === 'new' ? 'selected' : ''}>Новая</option>
                <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>В обработке</option>
                <option value="completed" ${order.status === 'completed' ? 'selected' : ''}>Завершена</option>
                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Отменена</option>
              </select>
              <button class="delete-btn px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200" data-order-id="${order.id}" data-order-type="${order.type}">
                Удалить
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // Получение бейджа статуса
    function getStatusBadge(status) {
      const badges = {
        'new': '<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Новая</span>',
        'processing': '<span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">В обработке</span>',
        'completed': '<span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Завершена</span>',
        'cancelled': '<span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Отменена</span>'
      };
      return badges[status] || badges['new'];
    }

    // Получение бейджа типа
    function getTypeBadge(type) {
      const badges = {
        'product': '<span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Заказ товаров</span>',
        'call': '<span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">Запрос на звонок</span>'
      };
      return badges[type] || '';
    }

    // Обновление статистики
    function updateStatistics() {
      const total = allOrders.length;
      const newOrders = allOrders.filter(o => o.status === 'new').length;
      const processing = allOrders.filter(o => o.status === 'processing').length;
      const completed = allOrders.filter(o => o.status === 'completed').length;
      
      totalCount.textContent = total;
      newCount.textContent = newOrders;
      processingCount.textContent = processing;
      completedCount.textContent = completed;
    }

    // Добавление обработчиков событий
    function addOrderEventListeners() {
      // Обработчики изменения статуса
      document.querySelectorAll('.status-select').forEach(select => {
        select.addEventListener('change', async (e) => {
          const orderId = e.target.dataset.orderId;
          const orderType = e.target.dataset.orderType;
          const newStatus = e.target.value;
          
          await updateOrderStatus(orderId, orderType, newStatus);
        });
      });
      
      // Обработчики удаления
      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', async (e) => {
          const orderId = e.target.dataset.orderId;
          const orderType = e.target.dataset.orderType;
          
          if (await window.adminModal?.confirmDelete(`заявку ${orderId}`)) {
            await deleteOrder(orderId, orderType);
          }
        });
      });
    }

    // Обновление статуса заявки
    async function updateOrderStatus(id, type, status) {
      try {
        const response = await fetch('/api/admin/orders', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ id, type, status })
        });
        
        const result = await response.json();
        
        if (result.success) {
          await window.adminModal?.showSuccess('Статус заявки обновлен');
          loadOrders(); // Перезагружаем список
        } else {
          throw new Error(result.error || 'Ошибка при обновлении статуса');
        }
      } catch (error) {
        console.error('Ошибка при обновлении статуса:', error);
        await window.adminModal?.showError('Ошибка при обновлении статуса: ' + error.message);
      }
    }

    // Удаление заявки
    async function deleteOrder(id, type) {
      try {
        const response = await fetch('/api/admin/orders', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ id, type })
        });
        
        const result = await response.json();
        
        if (result.success) {
          await window.adminModal?.showSuccess('Заявка удалена');
          loadOrders(); // Перезагружаем список
        } else {
          throw new Error(result.error || 'Ошибка при удалении заявки');
        }
      } catch (error) {
        console.error('Ошибка при удалении заявки:', error);
        await window.adminModal?.showError('Ошибка при удалении заявки: ' + error.message);
      }
    }

    // Вспомогательные функции для отображения состояний
    function showLoading() {
      loadingIndicator.classList.remove('hidden');
      ordersContainer.classList.add('hidden');
      emptyState.classList.add('hidden');
    }

    function hideLoading() {
      loadingIndicator.classList.add('hidden');
    }

    function showOrdersContainer() {
      ordersContainer.classList.remove('hidden');
      emptyState.classList.add('hidden');
    }

    function showEmptyState() {
      ordersContainer.classList.add('hidden');
      emptyState.classList.remove('hidden');
    }

    function showError(message) {
      hideLoading();
      ordersContainer.innerHTML = `<div class="text-center py-8 text-red-600">${message}</div>`;
      showOrdersContainer();
    }

    // Обработчики событий
    typeFilter.addEventListener('change', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    refreshBtn.addEventListener('click', loadOrders);

    // Загрузка данных при загрузке страницы
    document.addEventListener('DOMContentLoaded', loadOrders);
  </script>
</AdminLayout>
