---
import PageLayout from '../layouts/PageLayout.astro';
import CartItemsList from '../components/cart/CartItemsList.astro';
import OrderForm from '../components/cart/OrderForm.astro';
import ConsultationBlock from '../components/cart/ConsultationBlock.astro';
---

<PageLayout
  title="Оформление заказа"
  breadcrumbs={[
    { text: 'Корзина', url: '/cart' }
  ]}
>
  <!-- Основной контент страницы -->
  <section class="cart-page-content">
    <div class="container">
      <div class="cart-grid">
        <!-- Левая часть - Список товаров -->
        <div class="cart-items-column">
          <CartItemsList />
        </div>

        <!-- Правая часть - Форма оформления заказа -->
        <div class="order-form-column">
          <OrderForm />
        </div>
      </div>
    </div>
  </section>

  <!-- Блок консультации -->
  <ConsultationBlock />
</PageLayout>

<style>
  .cart-page-content {
    padding: 40px 0;
    min-height: 60vh;
  }

  .cart-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    align-items: start;
  }

  .cart-items-column,
  .order-form-column {
    width: 100%;
  }

  @media (max-width: 1024px) {
    .cart-grid {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .cart-page-content {
      padding: 24px 0;
    }
  }

  @media (max-width: 768px) {
    .cart-page-content {
      padding: 20px 0;
    }

    .cart-grid {
      gap: 20px;
    }
  }
</style>
