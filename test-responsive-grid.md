# Тест исправления проблемы с responsive настройками Grid

## Проблема
При изменении настроек колонок в основном селекторе, это же значение автоматически подставляется для Desktop (1025px-1280px) при сохранении.

## Внесенные изменения

### 1. API сохранения блока (src/pages/api/settings/save-block.js)
- Добавлено сохранение responsive настроек в строке 182

### 2. CompactGridManager.astro
- Изменена логика инициализации responsive настроек (строки 23-49)
- Теперь desktop.columns = Math.max(1, основные_колонки - 1) по умолчанию
- Убрана опция "Как в основных настройках" из селекторов
- Desktop селекторы теперь всегда показывают конкретные значения

### 3. ContentRenderer.astro
- Добавлены fallback CSS правила для desktop разрешения (строки 768-793)
- Теперь если data-grid-desktop-columns не установлен, используются основные настройки

### 4. Страница редактирования блока (edit.astro)
- Добавлен сбор responsive настроек при сохранении (строки 481-499)
- Responsive настройки теперь включаются в systemData (строка 559)
- Desktop настройки всегда сохраняются с fallback значениями

### 5. Логика updateGridSystemData
- Изменена логика сохранения desktop настроек (строки 802-810)
- Desktop настройки сохраняют точные значения из селекторов без fallback

### 6. Передача responsive настроек в CompactGridManager
- Исправлена передача responsive настроек в gridSystemSettings (строки 53-88)
- Теперь responsive настройки корректно передаются во всех случаях

## Тест

### Исходное состояние
Блок "jzwjp8cc" имеет:
- columns: 4
- gap: 2rem
- Нет responsive настроек

### Ожидаемое поведение
1. При загрузке страницы редактирования блока:
   - Основные настройки: 4 колонки, 2rem gap
   - Desktop селектор: 3 колонки (4-1), 2rem gap
   - Tablet селектор: 2 колонки, 0.8rem gap
   - Mobile селектор: 1 колонка, 0.5rem gap

2. При изменении основных настроек на 3 колонки:
   - Desktop селектор должен остаться 3 колонки (не меняется автоматически)
   - При сохранении desktop.columns = 3 (сохраняется текущее значение)

3. При изменении Desktop селектора на 2 колонки:
   - Desktop селектор: 2 колонки
   - При сохранении desktop.columns = 2

4. Для блока без responsive настроек (например, "un2mtib3" с 3 колонками):
   - При загрузке Desktop селектор должен показать 2 колонки (3-1)
   - Gap должен быть 1rem (как в основных настройках)

5. На фронтенде:
   - Для разрешений > 1280px: используются основные настройки
   - Для разрешений 1025px-1280px: используются desktop настройки
   - Desktop настройки всегда определены (не null)

## Проверка

### Тест 1: Блок с существующими responsive настройками
1. Открыть http://localhost:4322/admin/settings/pages/edit/contacts/block/edit
2. Найти блок "Форма для пользователя" (jzwjp8cc)
3. Проверить, что Desktop селектор показывает конкретное значение (2 колонки)
4. Изменить основные настройки и проверить, что Desktop селектор не меняется автоматически
5. Сохранить и проверить, что responsive настройки сохраняются корректно

### Тест 2: Блок без responsive настроек
1. Открыть http://localhost:4322/admin/settings/pages/edit/contacts/block/edit?blockId=un2mtib3
2. Найти блок "Тест гридов" (un2mtib3) с 3 колонками
3. Проверить, что Desktop селектор показывает 2 колонки (3-1)
4. Проверить, что Desktop gap показывает 1rem (как в основных)
5. Изменить основные настройки на 4 колонки и проверить, что Desktop остается 2 колонки
6. Сохранить и проверить JSON, что responsive настройки созданы автоматически

### Тест 3: Фронтенд отображение
1. Открыть http://localhost:4322/contacts
2. Изменить размер окна браузера до 1200px ширины
3. Проверить, что блоки отображаются с desktop настройками (меньше колонок чем в основных)
4. Изменить размер до 1400px и проверить, что используются основные настройки
