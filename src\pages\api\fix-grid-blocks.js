// API для исправления проблемных блоков с Grid-системой

import { loadPageSettings } from '../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../settings/utils/settingsSaver.js';

export async function POST({ request }) {
  try {
    console.log('🔧 Начинаем исправление проблемных блоков с Grid-системой');

    const settings = await loadPageSettings();
    let fixedCount = 0;
    let totalChecked = 0;
    const fixedBlocks = [];

    // Проходим по всем страницам и блокам
    for (const page of settings.pages) {
      if (!page.blocks) continue;

      for (const block of page.blocks) {
        totalChecked++;

        const gridSystem = block.displaySettings?.gridSystem;
        if (!gridSystem) continue;

        // Проверяем, есть ли проблема
        const isProblematic = (
          gridSystem.enabled &&
          gridSystem.mode === 'beginner' &&
          !gridSystem.beginnerSettings &&
          !gridSystem.columns // Нет упрощенных настроек
        );

        if (isProblematic) {
          console.log(`🔍 Найден проблемный блок: ${block.id} на странице ${page.id}`);

          // Исправляем блок
          const fixedGridSystem = {
            enabled: gridSystem.enabled,
            mode: 'simple', // Переводим в упрощенный режим
            items: gridSystem.items || [],
            previewActive: gridSystem.previewActive || false,
            // Добавляем недостающие настройки
            columns: 2,
            gap: { value: 1, unit: 'rem' },
            itemAlignment: 'stretch',
            contentAlignment: 'start'
          };

          // Обновляем блок
          block.displaySettings.gridSystem = fixedGridSystem;
          fixedCount++;

          fixedBlocks.push({
            pageId: page.id,
            blockId: block.id,
            blockName: block.name,
            oldSettings: gridSystem,
            newSettings: fixedGridSystem
          });

          console.log(`✅ Исправлен блок ${block.id}:`, fixedGridSystem);
        }
      }
    }

    // Сохраняем изменения, если есть исправления
    if (fixedCount > 0) {
      await savePageSettings(settings);
      console.log(`💾 Сохранено ${fixedCount} исправлений`);
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Проверено ${totalChecked} блоков, исправлено ${fixedCount}`,
      fixedCount,
      totalChecked,
      fixedBlocks
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Ошибка при исправлении блоков:', error);

    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function GET({ request }) {
  try {
    console.log('🔍 Анализ блоков с Grid-системой');

    const settings = await loadPageSettings();
    let totalBlocks = 0;
    let gridBlocks = 0;
    let problematicBlocks = 0;
    const analysis = [];

    // Анализируем все блоки
    for (const page of settings.pages) {
      if (!page.blocks) continue;

      for (const block of page.blocks) {
        totalBlocks++;

        const gridSystem = block.displaySettings?.gridSystem;
        if (!gridSystem) continue;

        gridBlocks++;

        // Определяем тип блока
        let blockType = 'unknown';
        let isProblematic = false;

        if (gridSystem.mode === 'simple' || (!gridSystem.mode && gridSystem.columns)) {
          blockType = 'simple';
        } else if (gridSystem.mode === 'beginner' && gridSystem.beginnerSettings) {
          blockType = 'beginner_complete';
        } else if (gridSystem.mode === 'beginner' && !gridSystem.beginnerSettings) {
          blockType = 'beginner_incomplete';
          isProblematic = true;
          problematicBlocks++;
        } else if (gridSystem.mode === 'pro') {
          blockType = 'pro';
        }

        analysis.push({
          pageId: page.id,
          blockId: block.id,
          blockName: block.name,
          blockType: block.type,
          gridEnabled: gridSystem.enabled,
          gridType: blockType,
          isProblematic,
          settings: gridSystem
        });
      }
    }

    return new Response(JSON.stringify({
      success: true,
      summary: {
        totalBlocks,
        gridBlocks,
        problematicBlocks
      },
      analysis
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Ошибка при анализе блоков:', error);

    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
