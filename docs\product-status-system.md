# Система статусов товаров

## Обзор

Система статусов товаров позволяет управлять видимостью товаров на сайте через административную панель. Система поддерживает три состояния товаров и обеспечивает гибкое управление публикацией контента.

## Статусы товаров

### 1. Черновик (draft)
- **Описание**: Товар находится в разработке или не готов к публикации
- **Видимость**: Не отображается на пользовательских страницах
- **Использование**: Для новых товаров, товаров в процессе редактирования
- **Цвет индикатора**: Серый

### 2. Опубликован (published)
- **Описание**: Товар активен и доступен для просмотра пользователями
- **Видимость**: Отображается на всех пользовательских страницах
- **Использование**: Для готовых к продаже товаров
- **Цвет индикатора**: Зеленый

### 3. Не опубликован (unpublished)
- **Описание**: Товар временно скрыт от пользователей
- **Видимость**: Не отображается на пользовательских страницах
- **Использование**: Для временно недоступных товаров, товаров на модерации
- **Цвет индикатора**: Красный

## Компоненты

### ProductStatusManager
Компонент для управления статусом товара в административной панели.

**Расположение**: `src/components/admin/ProductStatusManager.astro`

**Параметры**:
- `currentStatus` - текущий статус товара
- `productId` - ID товара для уникальности элементов
- `showLabel` - показывать ли подпись (по умолчанию true)
- `size` - размер компонента ('sm', 'md', 'lg')

**Использование**:
```astro
<ProductStatusManager 
  currentStatus={product.status || 'draft'}
  productId={product.id}
  showLabel={true}
  size="md"
/>
```

### ProductStatusBadge
Компонент для отображения статуса товара (только чтение).

**Расположение**: `src/components/admin/ProductStatusBadge.astro`

**Параметры**:
- `status` - статус товара для отображения
- `size` - размер бейджа ('sm', 'md', 'lg')

**Использование**:
```astro
<ProductStatusBadge 
  status={product.status || 'draft'}
  size="sm"
/>
```

## Интеграция

### Схема данных
Поле `status` добавлено в интерфейс Product:

```typescript
export interface Product {
  // ... другие поля
  status: ProductStatus; // 'draft' | 'published' | 'unpublished'
}
```

### API endpoints
Обновлены для поддержки статусов:

- `POST /api/admin/products` - создание товара с автоматическим статусом 'draft'
- `PUT /api/admin/products` - обновление товара с сохранением статуса

### Фильтрация на пользовательских страницах
Все пользовательские страницы фильтруют товары по статусу:

```javascript
// Показываем только опубликованные товары
const publishedProducts = products.filter(product => 
  product.status === 'published' || (!product.status && product.inStock)
);
```

**Затронутые страницы**:
- `/products` - каталог товаров
- `/products/[category]` - товары по категориям  
- `/products/[category]/[product]` - страница товара

### Административная панель
- **Список товаров** (`/admin/products`): отображение статусов, фильтрация по статусам
- **Создание товара** (`/admin/products/new`): автоматический статус 'draft'
- **Редактирование товара** (`/admin/products/edit/[id]`): управление статусом

## Миграция данных

Для существующих товаров создан скрипт миграции:

```bash
node scripts/migrate-product-status.js
```

**Логика миграции**:
- `inStock: true` → `status: 'published'`
- `inStock: false` → `status: 'unpublished'`

## Обратная совместимость

Система поддерживает обратную совместимость:
- Товары без поля `status` обрабатываются на основе `inStock`
- Поле `inStock` сохраняется для управления складскими остатками
- Разделение логики: `status` - видимость на сайте, `inStock` - наличие на складе

## Статистика

В админ-панели отображается статистика по статусам:
- Опубликовано: количество товаров со статусом 'published'
- Черновики: количество товаров со статусом 'draft'  
- Не опубликовано: количество товаров со статусом 'unpublished'

## Будущие возможности

Архитектура компонентов позволяет легко:
- Добавить новые статусы
- Переиспользовать компоненты для других сущностей
- Расширить функциональность (например, планирование публикации)
