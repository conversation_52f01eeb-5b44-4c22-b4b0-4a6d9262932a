// API для сохранения блоков страниц (система настроек)
import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';
import { isAuthenticated } from '../../../utils/auth';

/**
 * Валидирует настройки отображения
 * @param {Object} displaySettings - Настройки отображения
 * @returns {Array} - Массив ошибок валидации
 */
function validateDisplaySettings(displaySettings) {
  const errors = [];

  if (!displaySettings) return errors;

  // Валидация максимальной ширины
  if (displaySettings.maxWidth) {
    const { value, unit } = displaySettings.maxWidth;
    if (typeof value !== 'number' || value < 1 || value > 100) {
      errors.push('Максимальная ширина должна быть числом от 1 до 100');
    }
    if (!['px', '%'].includes(unit)) {
      errors.push('Единица максимальной ширины должна быть px или %');
    }
  }

  // Валидация выравнивания
  if (displaySettings.alignment && !['left', 'center', 'right'].includes(displaySettings.alignment)) {
    errors.push('Выравнивание должно быть left, center или right');
  }

  // Валидация отступов
  ['padding', 'margin'].forEach(type => {
    if (displaySettings[type]) {
      const { top, right, bottom, left, unit } = displaySettings[type];
      [top, right, bottom, left].forEach((value, index) => {
        const sides = ['top', 'right', 'bottom', 'left'];
        if (typeof value !== 'number' || Math.abs(value) > 1000) {
          errors.push(`${type === 'padding' ? 'Внутренний' : 'Внешний'} отступ ${sides[index]} должен быть числом от -1000 до 1000`);
        }
      });
      if (!['px', '%'].includes(unit)) {
        errors.push(`Единица ${type === 'padding' ? 'внутренних' : 'внешних'} отступов должна быть px или %`);
      }
    }
  });

  // Валидация grid колонок
  if (displaySettings.gridColumns) {
    if (typeof displaySettings.gridColumns !== 'number' ||
        displaySettings.gridColumns < 1 ||
        displaySettings.gridColumns > 12) {
      errors.push('Количество элементов в строке должно быть числом от 1 до 12');
    }
  }

  // Валидация отступов между элементами
  if (displaySettings.elementGap) {
    const { value, unit } = displaySettings.elementGap;
    if (typeof value !== 'number' || value < 0 || value > 100) {
      errors.push('Отступы между элементами должны быть числом от 0 до 100');
    }
    if (!['px', 'rem'].includes(unit)) {
      errors.push('Единица отступов между элементами должна быть px или rem');
    }
  }

  // Валидация упрощенной Grid-системы
  if (displaySettings.gridSystem) {
    const gridSystem = displaySettings.gridSystem;

    if (gridSystem.enabled) {
      // Валидация режима
      if (!['simple', 'beginner', 'pro'].includes(gridSystem.mode)) {
        errors.push('Режим Grid-системы должен быть simple, beginner или pro');
      }

      // Валидация упрощенной Grid-системы
      if (gridSystem.mode === 'simple') {
        const { columns, gap, itemAlignment, contentAlignment } = gridSystem;

        if (typeof columns !== 'number' || columns < 1 || columns > 12) {
          errors.push('Количество колонок должно быть числом от 1 до 12');
        }

        if (gap && (typeof gap.value !== 'number' || gap.value < 0 || gap.value > 100)) {
          errors.push('Отступы Grid должны быть числом от 0 до 100');
        }

        if (gap && !['px', 'rem', 'em', '%'].includes(gap.unit)) {
          errors.push('Единица отступов Grid должна быть px, rem, em или %');
        }

        if (itemAlignment && !['stretch', 'start', 'end', 'center'].includes(itemAlignment)) {
          errors.push('Выравнивание элементов должно быть stretch, start, end или center');
        }

        if (contentAlignment && !['start', 'end', 'center', 'space-around', 'space-between', 'space-evenly'].includes(contentAlignment)) {
          errors.push('Выравнивание контента должно быть одним из допустимых значений');
        }
      }

      // Валидация настроек новичка (для обратной совместимости)
      if (gridSystem.mode === 'beginner' && gridSystem.beginnerSettings) {
        const { columns, gap, itemAlignment, contentAlignment } = gridSystem.beginnerSettings;

        if (typeof columns !== 'number' || columns < 1 || columns > 12) {
          errors.push('Количество колонок должно быть числом от 1 до 12');
        }

        if (gap && (typeof gap.value !== 'number' || gap.value < 0 || gap.value > 100)) {
          errors.push('Отступы Grid должны быть числом от 0 до 100');
        }

        if (gap && !['px', 'rem', 'em'].includes(gap.unit)) {
          errors.push('Единица отступов Grid должна быть px, rem или em');
        }

        if (itemAlignment && !['stretch', 'start', 'end', 'center'].includes(itemAlignment)) {
          errors.push('Выравнивание элементов должно быть stretch, start, end или center');
        }

        if (contentAlignment && !['start', 'end', 'center', 'stretch', 'space-around', 'space-between', 'space-evenly'].includes(contentAlignment)) {
          errors.push('Выравнивание контента должно быть одним из допустимых значений');
        }
      }

      // Валидация настроек профи (для обратной совместимости)
      if (gridSystem.mode === 'pro' && gridSystem.proSettings) {
        const { gap } = gridSystem.proSettings;

        if (gap && (typeof gap.value !== 'number' || gap.value < 0 || gap.value > 100)) {
          errors.push('Отступы Grid (профи) должны быть числом от 0 до 100');
        }

        if (gap && !['px', 'rem', 'em'].includes(gap.unit)) {
          errors.push('Единица отступов Grid (профи) должна быть px, rem или em');
        }
      }

      // Валидация элементов
      if (gridSystem.items && Array.isArray(gridSystem.items)) {
        if (gridSystem.items.length > 100) {
          errors.push('Слишком много элементов в Grid (максимум 100)');
        }
      }
    }
  }

  return errors;
}

/**
 * Парсит настройки Grid-системы из данных формы
 * @param {Object} data - Данные формы
 * @returns {Object|null} - Объект настроек Grid-системы или null
 */
function parseGridSystemSettings(data) {
  // Сначала пробуем получить данные из gridSystemData (приоритет)
  if (data.gridSystemData) {
    try {
      const systemData = JSON.parse(data.gridSystemData);
      console.log('🔍 Парсинг Grid-системы из gridSystemData:', systemData);

      if (!systemData.enabled) {
        console.log('Grid-система отключена в gridSystemData, возвращаем null');
        return null;
      }

      // Упрощенная Grid-система без профилей
      const result = {
        enabled: true,
        mode: 'simple', // Простой режим без профилей
        items: systemData.items || [],
        previewActive: systemData.previewActive || false,
        // Прямые настройки без вложенности
        columns: systemData.columns || 2,
        gap: systemData.gap || { value: 1, unit: 'rem' },
        itemAlignment: systemData.itemAlignment || 'stretch',
        contentAlignment: systemData.contentAlignment || 'start',
        // Сохраняем responsive настройки
        responsive: systemData.responsive || null
      };

      console.log('✅ Упрощенная Grid-система распарсена:', result);
      return result;
    } catch (e) {
      console.error('Ошибка парсинга gridSystemData:', e);
      // Продолжаем с fallback логикой
    }
  }

  // Fallback: используем логику с отдельными полями
  const enabled = data.gridEnabled === 'on';

  console.log('🔍 Парсинг Grid-системы (fallback):', {
    gridEnabled: data.gridEnabled,
    enabled: enabled,
    gridMode: data.gridMode
  });

  if (!enabled) {
    console.log('Grid-система отключена, возвращаем null');
    return null;
  }

  // Упрощенная Grid-система
  const gridSystem = {
    enabled: true,
    mode: 'simple',
    items: [],
    columns: Number(data.beginnerColumns) || 2,
    gap: {
      value: Number(data.beginnerGapValue) || 1,
      unit: data.beginnerGapUnit || 'rem'
    },
    itemAlignment: data.beginnerItemAlignment || 'stretch',
    contentAlignment: data.beginnerContentAlignment || 'start'
  };

  // Парсим элементы Grid
  if (data.gridItemsData) {
    try {
      gridSystem.items = JSON.parse(data.gridItemsData);
    } catch (error) {
      console.error('Ошибка парсинга элементов Grid:', error);
      gridSystem.items = [];
    }
  }

  console.log('✅ Grid-система (fallback) распарсена:', gridSystem);
  return gridSystem;
}

/**
 * Парсит настройки отображения из данных формы
 * @param {Object} data - Данные формы
 * @returns {Object|null} - Объект настроек отображения или null
 */
function parseDisplaySettings(data) {
  const displaySettings = {};

  // Максимальная ширина
  if (data['displaySettings.maxWidth.value']) {
    displaySettings.maxWidth = {
      value: Number(data['displaySettings.maxWidth.value']),
      unit: data['displaySettings.maxWidth.unit'] || '%'
    };
  }

  // Выравнивание
  if (data['displaySettings.alignment']) {
    displaySettings.alignment = data['displaySettings.alignment'];
  }

  // Внутренние отступы (padding)
  if (data['displaySettings.padding.top'] !== undefined ||
      data['displaySettings.padding.right'] !== undefined ||
      data['displaySettings.padding.bottom'] !== undefined ||
      data['displaySettings.padding.left'] !== undefined) {
    displaySettings.padding = {
      top: Number(data['displaySettings.padding.top']) || 0,
      right: Number(data['displaySettings.padding.right']) || 0,
      bottom: Number(data['displaySettings.padding.bottom']) || 0,
      left: Number(data['displaySettings.padding.left']) || 0,
      unit: data['displaySettings.padding.unit'] || 'px'
    };
  }

  // Внешние отступы (margin)
  if (data['displaySettings.margin.top'] !== undefined ||
      data['displaySettings.margin.right'] !== undefined ||
      data['displaySettings.margin.bottom'] !== undefined ||
      data['displaySettings.margin.left'] !== undefined) {
    displaySettings.margin = {
      top: Number(data['displaySettings.margin.top']) || 0,
      right: Number(data['displaySettings.margin.right']) || 0,
      bottom: Number(data['displaySettings.margin.bottom']) || 0,
      left: Number(data['displaySettings.margin.left']) || 0,
      unit: data['displaySettings.margin.unit'] || 'px'
    };
  }

  // Новая Grid-система
  const gridSystem = parseGridSystemSettings(data);
  if (gridSystem) {
    displaySettings.gridSystem = gridSystem;
  } else {
    // Если Grid-система отключена, явно устанавливаем null для удаления
    displaySettings.gridSystem = null;
  }

  // Возвращаем настройки только если есть хотя бы одно значение
  return Object.keys(displaySettings).length > 0 ? displaySettings : null;
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response('Не авторизован', { status: 401 });
    }

    const formData = await request.formData();
    const data = Object.fromEntries(formData.entries());

    // Отладочная информация
    console.log('📝 Получены данные для сохранения блока:', {
      pageId: data.id,
      blockId: data.blockId,
      blockName: data.blockName,
      gridEnabled: data.gridEnabled,
      gridEnabledType: typeof data.gridEnabled,
      gridMode: data.gridMode,
      hasGridItemsData: !!data.gridItemsData,
      beginnerColumns: data.beginnerColumns,
      beginnerGapValue: data.beginnerGapValue,
      beginnerGapUnit: data.beginnerGapUnit,
      contentLength: data.content ? data.content.length : 0,
      contentPreview: data.content ? data.content.substring(0, 100) + '...' : 'пустой'
    });

    // Дополнительная отладка для Grid-системы
    console.log('🔍 Все данные формы:', Object.keys(data).filter(key => key.includes('grid')).reduce((obj, key) => {
      obj[key] = data[key];
      return obj;
    }, {}));

    const pageId = data.id;
    const blockId = data.blockId;
    const blockName = data.blockName;
    const lang = data.lang || 'ru';
    const content = data.content || '';

    // Парсим настройки отображения
    const displaySettings = parseDisplaySettings(data);

    // Валидируем настройки отображения
    if (displaySettings) {
      const validationErrors = validateDisplaySettings(displaySettings);
      if (validationErrors.length > 0) {
        return new Response(
          JSON.stringify({
            error: 'Ошибки валидации настроек отображения',
            details: validationErrors
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === pageId);

    if (pageIdx === -1) {
      return new Response('Страница не найдена', { status: 404 });
    }

    const page = pages[pageIdx];
    let blocks = page.blocks || [];
    let block = blocks.find(b => b.id === blockId);

    // Вычисляем правильный порядок
    let order = Number(data.order);
    if (!order || order <= 0) {
      // Автоматическое назначение порядка
      const maxOrder = blocks.length > 0 ? Math.max(...blocks.map(b => b.order || 0)) : 0;
      order = maxOrder + 1;
    }

    if (!block) {
      // Новый блок
      block = {
        id: blockId,
        name: blockName || '',
        type: data.type || 'text',
        order: order,
        enabled: data.enabled === 'on',
        content: { ru: '', en: '' }
      };

      // Добавляем настройки отображения если они есть
      if (displaySettings) {
        block.displaySettings = displaySettings;
      }

      blocks.push(block);
    } else {
      // Обновление существующего блока
      if (blockName !== undefined) block.name = blockName;
      if (data.type) block.type = data.type;
      if (data.order) block.order = Number(data.order);
      if (data.enabled !== undefined) block.enabled = data.enabled === 'on';

      // Обновляем настройки отображения
      if (displaySettings) {
        // Объединяем новые настройки с существующими
        block.displaySettings = {
          ...block.displaySettings,
          ...displaySettings
        };

        // Если gridSystem равно null, удаляем его из настроек
        if (block.displaySettings.gridSystem === null) {
          console.log('🗑️ Удаляем настройки Grid-системы из блока');
          delete block.displaySettings.gridSystem;

          // Если остались только настройки с null значениями, очищаем объект
          const hasValidSettings = Object.values(block.displaySettings).some(value => value !== null && value !== undefined);
          if (!hasValidSettings) {
            console.log('🗑️ Удаляем весь объект displaySettings (нет валидных настроек)');
            delete block.displaySettings;
          }
        }
      }
    }

    // Обновить контент для языка только если контент не пустой или это новый блок
    block.content = block.content || { ru: '', en: '' };

    const isNewBlock = !blocks.find(b => b.id === blockId);
    const shouldUpdateContent = content || isNewBlock;

    console.log('🔍 Обновление контента:', {
      isNewBlock,
      contentLength: content ? content.length : 0,
      shouldUpdateContent,
      currentContent: block.content[lang] ? block.content[lang].substring(0, 50) + '...' : 'пустой'
    });

    // Если контент не пустой или это новый блок, обновляем его
    if (shouldUpdateContent) {
      block.content[lang] = content;
      console.log('✅ Контент обновлен');

      // Если Grid-система включена и есть контент, автоматически заполняем items
      if (block.displaySettings?.gridSystem?.enabled && content) {
        try {
          const editorData = JSON.parse(content);
          if (editorData.blocks && Array.isArray(editorData.blocks)) {
            // Преобразуем блоки EditorJS в элементы Grid
            const gridItems = editorData.blocks.map((editorBlock, index) => ({
              id: editorBlock.id || `block-${index}`,
              type: editorBlock.type || 'paragraph',
              order: index + 1,
              editorData: editorBlock.data || {},
              gridColumn: undefined,
              gridRow: undefined
            }));

            // Обновляем items в gridSystem
            block.displaySettings.gridSystem.items = gridItems;
            console.log(`✅ Grid items автоматически заполнены: ${gridItems.length} элементов`);
          }
        } catch (parseError) {
          console.warn('⚠️ Не удалось распарсить контент для Grid items:', parseError.message);
        }
      }
    } else {
      console.log('⚠️ Контент не обновлен - пустой контент для существующего блока');
    }

    // Обновить массив блоков и страницу
    page.blocks = blocks;
    pages[pageIdx] = page;
    settings.pages = pages;

    await savePageSettings(settings);

    return new Response(null, {
      status: 303,
      headers: { Location: `/admin/settings/pages/edit/${pageId}` }
    });

  } catch (error) {
    console.error('❌ Ошибка при сохранении блока:', error);

    // Для HTML форм возвращаем редирект с ошибкой в URL
    const errorMessage = encodeURIComponent(`Ошибка при сохранении блока: ${error.message}`);
    return new Response(null, {
      status: 303,
      headers: {
        Location: `/admin/settings/pages/edit/${pageId}?error=${errorMessage}`
      }
    });
  }
}
