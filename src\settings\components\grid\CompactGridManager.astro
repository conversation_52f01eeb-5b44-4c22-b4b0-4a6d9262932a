---
import type { GridSystemSettings } from '../../types';

export interface Props {
  settings: GridSystemSettings;
  blockId: string;
}

const { settings, blockId } = Astro.props;

// Значения по умолчанию (упрощенная версия)
const currentSettings = {
  enabled: settings?.enabled || false,
  mode: 'container' as const, // Всегда используем режим контейнеров
  columns: settings?.columns || 2,
  gap: settings?.gap || { value: 1, unit: 'rem' },
  itemAlignment: settings?.itemAlignment || 'stretch',
  contentAlignment: settings?.contentAlignment || 'start',
  autoRows: settings?.autoRows || 'minmax(200px, auto)',
  justifyItems: settings?.justifyItems || 'stretch',
  alignContent: settings?.alignContent || 'start',
  justifyContent: settings?.justifyContent || 'start',
  responsive: {
    desktop: {
      // Если есть сохраненное значение - используем его, иначе fallback (основные - 1)
      columns: settings?.responsive?.desktop?.columns !== undefined
        ? settings.responsive.desktop.columns
        : Math.max(1, (settings?.columns || 2) - 1),
      gap: settings?.responsive?.desktop?.gap || settings?.gap || { value: 1, unit: 'rem' }
    },
    tablet: {
      columns: settings?.responsive?.tablet?.columns !== undefined
        ? settings.responsive.tablet.columns
        : Math.min(settings?.columns || 3, 2),
      gap: settings?.responsive?.tablet?.gap || { value: 0.8, unit: 'rem' }
    },
    mobile: {
      columns: settings?.responsive?.mobile?.columns !== undefined
        ? settings.responsive.mobile.columns
        : 1,
      gap: settings?.responsive?.mobile?.gap || { value: 0.5, unit: 'rem' }
    }
  },
  previewActive: settings?.previewActive || false,
  items: settings?.items || []
};

// Отладочная информация
console.log('🔍 CompactGridManager currentSettings для блока', blockId, ':', {
  mainColumns: currentSettings.columns,
  desktopColumns: currentSettings.responsive.desktop.columns,
  originalSettings: settings?.responsive?.desktop,
  calculatedDesktop: Math.max(1, (settings?.columns || 2) - 1)
});
---

<div class="compact-grid-manager" data-block-id={blockId}>

  <!-- Переключатель включения Grid -->
  <div class="grid-toggle mb-4">
    <label class="flex items-center space-x-3 cursor-pointer">
      <input
        type="checkbox"
        id={`grid-enabled-${blockId}`}
        name="gridEnabled"
        checked={currentSettings.enabled}
        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
      />
      <span class="text-sm font-medium text-gray-900">Включить Grid-систему</span>
    </label>
  </div>

  <!-- Настройки Grid -->
  <div class="grid-settings-container" style={currentSettings.enabled ? '' : 'display: none;'}>

    <!-- Информация о режиме контейнеров -->
    <div class="container-info mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 mb-2">📦 Режим контейнеров</h4>
      <p class="text-xs text-blue-700 mb-2">
        Используйте инструмент "Grid Container" в редакторе для группировки элементов.
        Настройки grid будут применяться к контейнерам, а не к отдельным элементам.
      </p>
      <div class="text-xs text-blue-600">
        <p>• Нажмите "+" в редакторе → выберите "Grid Container"</p>
        <p>• Активируйте контейнер кликом для добавления элементов</p>
      </div>
    </div>

  <!-- Скрытые поля для передачи данных -->
  <input type="hidden" name="gridSystemData" id={`grid-system-data-${blockId}`} value={JSON.stringify(currentSettings)} />

    <!-- Основные настройки Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">




      <!-- Левая панель - Настройки -->
      <div class="settings-panel">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Настройки Grid</h4>

        <!-- Количество колонок -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Колонки:</label>
          <select
            name="beginnerColumns"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1" selected={currentSettings.columns === 1}>1 колонка</option>
            <option value="2" selected={currentSettings.columns === 2}>2 колонки</option>
            <option value="3" selected={currentSettings.columns === 3}>3 колонки</option>
            <option value="4" selected={currentSettings.columns === 4}>4 колонки</option>
            <option value="5" selected={currentSettings.columns === 5}>5 колонок</option>
            <option value="6" selected={currentSettings.columns === 6}>6 колонок</option>
          </select>
        </div>

        <!-- Отступы между элементами -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Отступы:</label>
          <div class="flex space-x-2">
            <input
              type="number"
              name="beginnerGapValue"
              value={currentSettings.gap.value}
              min="0"
              max="10"
              step="0.1"
              class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <select
              name="beginnerGapUnit"
              class="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="rem" selected={currentSettings.gap.unit === 'rem'}>rem</option>
              <option value="px" selected={currentSettings.gap.unit === 'px'}>px</option>
              <option value="em" selected={currentSettings.gap.unit === 'em'}>em</option>
              <option value="%" selected={currentSettings.gap.unit === '%'}>%</option>
            </select>
          </div>
        </div>

        <!-- Выравнивание элементов -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Выравнивание элементов:</label>
          <select
            name="beginnerItemAlignment"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="stretch" selected={currentSettings.itemAlignment === 'stretch'}>Растянуть</option>
            <option value="start" selected={currentSettings.itemAlignment === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.itemAlignment === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.itemAlignment === 'end'}>Конец</option>
          </select>
        </div>


      </div>

      <!-- Правая панель - Превью -->
        <div class="preview-panel">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Превью</h4>
          <div class="preview-container bg-gray-50 border border-gray-200 rounded p-3 min-h-[200px]">
            <div id={`grid-preview-${blockId}`} class="grid-preview-content">
              {!currentSettings.previewActive ? (
                <div class="text-center text-gray-500 text-sm py-8">
                  <p>Создайте элементы в редакторе</p>
                  <p>и нажмите "Показать превью"</p>
                </div>
              ) : (
                <div class="text-center text-gray-500 text-sm py-8">
                  <p>Превью будет отображено здесь</p>
                </div>
              )}
            </div>

            <!-- Кнопка превью Grid -->
            <div class="mt-3 text-center" id={`grid-preview-button-container-${blockId}`}>
              <button
                type="button"
                id={`grid-preview-button-${blockId}`}
                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              </button>
              <p class="text-xs text-gray-500 mt-2">Создайте элементы в редакторе и нажмите кнопку для превью</p>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Дополнительные настройки Grid -->
    <div class="advanced-settings mt-6" style={currentSettings.enabled ? '' : 'display: none;'}>
      <h4 class="text-sm font-medium text-gray-900 mb-3">Дополнительные настройки</h4>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Автоматическая высота строк -->
        <div class="setting-group">
          <label class="block text-xs font-medium text-gray-700 mb-1">Высота строк:</label>
          <input
            type="text"
            name="autoRows"
            value={currentSettings.autoRows || 'minmax(200px, auto)'}
            placeholder="minmax(200px, auto)"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <p class="text-xs text-gray-500 mt-1">CSS значение для grid-auto-rows</p>
        </div>

        <!-- Выравнивание элементов по горизонтали -->
        <div class="setting-group">
          <label class="block text-xs font-medium text-gray-700 mb-1">Выравнивание по горизонтали:</label>
          <select
            name="justifyItems"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="stretch" selected={currentSettings.justifyItems === 'stretch'}>Растянуть</option>
            <option value="start" selected={currentSettings.justifyItems === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.justifyItems === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.justifyItems === 'end'}>Конец</option>
          </select>
        </div>

        <!-- Выравнивание содержимого Grid -->
        <div class="setting-group">
          <label class="block text-xs font-medium text-gray-700 mb-1">Выравнивание содержимого Grid:</label>
          <select
            name="alignContent"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="start" selected={currentSettings.alignContent === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.alignContent === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.alignContent === 'end'}>Конец</option>
            <option value="space-between" selected={currentSettings.alignContent === 'space-between'}>Между</option>
            <option value="space-around" selected={currentSettings.alignContent === 'space-around'}>Вокруг</option>
            <option value="space-evenly" selected={currentSettings.alignContent === 'space-evenly'}>Равномерно</option>
          </select>
        </div>

        <!-- Распределение содержимого Grid -->
        <div class="setting-group">
          <label class="block text-xs font-medium text-gray-700 mb-1">Распределение содержимого Grid:</label>
          <select
            name="justifyContent"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="start" selected={currentSettings.justifyContent === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.justifyContent === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.justifyContent === 'end'}>Конец</option>
            <option value="space-between" selected={currentSettings.justifyContent === 'space-between'}>Между</option>
            <option value="space-around" selected={currentSettings.justifyContent === 'space-around'}>Вокруг</option>
            <option value="space-evenly" selected={currentSettings.justifyContent === 'space-evenly'}>Равномерно</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Адаптивные настройки -->
    <div class="responsive-settings mt-6" style={currentSettings.enabled ? '' : 'display: none;'}>
      <h4 class="text-sm font-medium text-gray-900 mb-3">Адаптивные настройки</h4>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- Desktop -->
        <div class="setting-group">
          <h5 class="text-xs font-semibold text-gray-800 mb-2">🖥️ Desktop (1024px - 1280px)</h5>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Колонки:</label>
              <select
                name="desktopColumns"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="1" selected={currentSettings.responsive?.desktop?.columns === 1}>1</option>
                <option value="2" selected={currentSettings.responsive?.desktop?.columns === 2}>2</option>
                <option value="3" selected={currentSettings.responsive?.desktop?.columns === 3}>3</option>
                <option value="4" selected={currentSettings.responsive?.desktop?.columns === 4}>4</option>
                <option value="5" selected={currentSettings.responsive?.desktop?.columns === 5}>5</option>
                <option value="6" selected={currentSettings.responsive?.desktop?.columns === 6}>6</option>
              </select>
            </div>
            <div class="flex space-x-1">
              <input
                type="number"
                name="desktopGapValue"
                value={currentSettings.responsive?.desktop?.gap?.value}
                min="0"
                max="10"
                step="0.1"
                class="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <select
                name="desktopGapUnit"
                class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="rem" selected={currentSettings.responsive?.desktop?.gap?.unit === 'rem'}>rem</option>
                <option value="px" selected={currentSettings.responsive?.desktop?.gap?.unit === 'px'}>px</option>
                <option value="em" selected={currentSettings.responsive?.desktop?.gap?.unit === 'em'}>em</option>
                <option value="%" selected={currentSettings.responsive?.desktop?.gap?.unit === '%'}>%</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Tablet -->
        <div class="setting-group">
          <h5 class="text-xs font-semibold text-gray-800 mb-2">📱 Tablet</h5>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Колонки:</label>
              <select
                name="tabletColumns"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="1" selected={currentSettings.responsive?.tablet?.columns === 1}>1</option>
                <option value="2" selected={currentSettings.responsive?.tablet?.columns === 2}>2</option>
                <option value="3" selected={currentSettings.responsive?.tablet?.columns === 3}>3</option>
                <option value="4" selected={currentSettings.responsive?.tablet?.columns === 4}>4</option>
              </select>
            </div>
            <div class="flex space-x-1">
              <input
                type="number"
                name="tabletGapValue"
                value={currentSettings.responsive?.tablet?.gap?.value || 0.8}
                min="0"
                max="10"
                step="0.1"
                class="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <select
                name="tabletGapUnit"
                class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="rem" selected={(currentSettings.responsive?.tablet?.gap?.unit || 'rem') === 'rem'}>rem</option>
                <option value="px" selected={(currentSettings.responsive?.tablet?.gap?.unit || 'rem') === 'px'}>px</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Mobile -->
        <div class="setting-group">
          <h5 class="text-xs font-semibold text-gray-800 mb-2">📱 Mobile</h5>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Колонки:</label>
              <select
                name="mobileColumns"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="1" selected={currentSettings.responsive?.mobile?.columns === 1}>1</option>
                <option value="2" selected={currentSettings.responsive?.mobile?.columns === 2}>2</option>
                <option value="3" selected={currentSettings.responsive?.mobile?.columns === 3}>3</option>
              </select>
            </div>
            <div class="flex space-x-1">
              <input
                type="number"
                name="mobileGapValue"
                value={currentSettings.responsive?.mobile?.gap?.value || 0.5}
                min="0"
                max="10"
                step="0.1"
                class="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <select
                name="mobileGapUnit"
                class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="rem" selected={(currentSettings.responsive?.mobile?.gap?.unit || 'rem') === 'rem'}>rem</option>
                <option value="px" selected={(currentSettings.responsive?.mobile?.gap?.unit || 'rem') === 'px'}>px</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  </div>

<style>
  .compact-grid-manager {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .setting-group {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
    padding: 0.75rem;
  }

  .preview-container {
    position: relative;
    overflow: hidden;
  }

  .grid-preview-content {
    display: grid;
    gap: 0.5rem;
    min-height: 150px;
  }

  /* Анимации для сворачивания/разворачивания */
  .grid-settings-container {
    transition: all 0.3s ease;
  }

  .grid-main-panel {
    transition: all 0.3s ease;
  }

  /* Стили для превью Grid */
  .grid-preview-content {
    min-height: 200px;
    display: grid;
    gap: 1rem;
    border: 2px dashed #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: #f9fafb;
  }

  .grid-preview-item {
    background: #dbeafe;
    border: 2px dashed #3b82f6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: #1e40af;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .grid-preview-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 0.875rem;
    color: #374151;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .grid-preview-item h1, .grid-preview-item h2, .grid-preview-item h3,
  .grid-preview-item h4, .grid-preview-item h5, .grid-preview-item h6 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
  }

  .grid-preview-item p {
    margin: 0 0 0.5rem 0;
  }

  .grid-preview-item img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
  }

  .grid-preview-item ul, .grid-preview-item ol {
    margin: 0;
    padding-left: 1.5rem;
  }

  /* Адаптивность */
  @media (max-width: 1024px) {
    .grid-main-panel .grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<script>
  console.log('CompactGridManager: Скрипт загружен');

  document.addEventListener('DOMContentLoaded', function() {
    console.log('CompactGridManager: DOM загружен, инициализация...');

    // Находим все компактные Grid-менеджеры на странице
    const gridManagers = document.querySelectorAll('.compact-grid-manager');
    console.log(`CompactGridManager: Найдено ${gridManagers.length} менеджеров`);

    gridManagers.forEach((manager, index) => {
      const blockId = manager.dataset.blockId;
      console.log(`CompactGridManager: Инициализация менеджера ${index + 1} для блока ${blockId}`);

      const gridToggle = manager.querySelector('input[name="gridEnabled"]');
      const settingsContainer = manager.querySelector('.grid-settings-container');
      const mainPanel = manager.querySelector('.grid-main-panel');

      console.log('CompactGridManager: Элементы найдены:', {
        gridToggle: !!gridToggle,
        settingsContainer: !!settingsContainer,
        mainPanel: !!mainPanel
      });

      // Отладка начального состояния
      if (gridToggle) {
        console.log(`🔍 Начальное состояние Grid для блока ${blockId}:`, {
          checked: gridToggle.checked,
          id: gridToggle.id,
          name: gridToggle.name
        });
      }

      if (!gridToggle || !settingsContainer) {
        console.error('CompactGridManager: Не найдены обязательные элементы');
        return;
      }

      // Функция для показа/скрытия настроек
      function toggleGridSettings() {
        const isEnabled = gridToggle.checked;
        console.log(`🔄 CompactGridManager: toggleGridSettings вызвана для блока ${blockId}, isEnabled = ${isEnabled}`);
        console.log(`🔍 Стек вызова:`, new Error().stack);

        settingsContainer.style.display = isEnabled ? 'block' : 'none';
        console.log(`CompactGridManager: settingsContainer.style.display = ${settingsContainer.style.display}`);

        // Управляем видимостью дополнительных секций
        const advancedSettings = manager.querySelector('.advanced-settings');
        const responsiveSettings = manager.querySelector('.responsive-settings');

        if (advancedSettings) {
          advancedSettings.style.display = isEnabled ? 'block' : 'none';
        }

        if (responsiveSettings) {
          responsiveSettings.style.display = isEnabled ? 'block' : 'none';
        }

        // Обновляем скрытые поля gridSystemData
        updateGridSystemData();

        // Уведомляем другие компоненты об изменении состояния Grid
        const event = new CustomEvent('gridToggled', {
          detail: { blockId, enabled: isEnabled }
        });
        document.dispatchEvent(event);

        console.log(`CompactGridManager: Grid ${isEnabled ? 'включен' : 'отключен'} для блока ${blockId}`);
      }



      // Инициализация режима контейнеров (всегда активен)
      function initContainerMode() {
        // Показываем информацию о режиме контейнеров
        showContainerModeInfo();

        // Обновляем данные
        updateGridSystemData();

        console.log(`Режим контейнеров активирован для блока ${blockId}`);
      }

      // Функция для показа информации о режиме контейнеров
      function showContainerModeInfo() {
        const previewContainer = document.getElementById(`grid-preview-${blockId}`);
        if (previewContainer) {
          previewContainer.innerHTML = `
            <div class="text-center text-blue-600 text-sm py-8">
              <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h4 class="font-medium text-gray-900 mb-2">Режим контейнеров активен</h4>
              <p class="text-gray-600 mb-4">Используйте инструмент "Grid Container" в редакторе для создания контейнеров</p>
              <div class="text-xs text-gray-500">
                <p>1. Нажмите "+" в редакторе</p>
                <p>2. Выберите "Grid Container"</p>
                <p>3. Добавляйте элементы в контейнер</p>
              </div>
            </div>
          `;
        }
      }

      // Функция для настройки кнопки превью
      function setupGridPreviewButton() {
        const previewButton = document.getElementById(`grid-preview-button-${blockId}`);
        const previewContainer = document.getElementById(`grid-preview-${blockId}`);

        if (previewButton && previewContainer) {
          previewButton.addEventListener('click', async function() {
            try {
              // Показываем состояние загрузки
              previewButton.disabled = true;
              previewButton.innerHTML = `
                <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Загрузка...
              `;

              // Получаем данные из редактора
              const editorInstance = window.editorInstances?.content;
              if (!editorInstance) {
                throw new Error('Редактор не найден');
              }

              const editorData = await editorInstance.save();

              if (!editorData.blocks || editorData.blocks.length === 0) {
                throw new Error('В редакторе нет элементов для отображения в Grid');
              }

              // Передаем данные в Grid-систему для превью
              if (window.EditorJSGridParser) {
                const gridItems = window.EditorJSGridParser.parseBlocksForGrid(editorData.blocks);
                renderGridPreview(gridItems);
                console.log(`Grid превью обновлено с ${gridItems.length} элементами`);
              } else {
                throw new Error('EditorJSGridParser не загружен');
              }

              // Восстанавливаем кнопку
              previewButton.disabled = false;
              previewButton.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              `;

            } catch (error) {
              console.error('Ошибка при создании превью Grid:', error);

              // Показываем ошибку в превью
              previewContainer.innerHTML = `
                <div class="text-center text-red-500 text-sm py-8">
                  <p>Ошибка: ${error.message}</p>
                </div>
              `;

              // Восстанавливаем кнопку
              previewButton.disabled = false;
              previewButton.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              `;
            }
          });
        }
      }

      // Функция для рендеринга превью Grid
      function renderGridPreview(items) {
        const previewContainer = document.getElementById(`grid-preview-${blockId}`);
        if (!previewContainer) return;

        const enabled = gridToggle.checked;
        if (!enabled) {
          previewContainer.innerHTML = '<p class="text-gray-500 text-center py-8">Grid отключен</p>';
          return;
        }

        // Всегда используем режим контейнеров
        renderContainerPreview(previewContainer);
      }

      // Функция для рендеринга превью в простом режиме
      function renderSimplePreview(items, container) {
        const columns = parseInt(manager.querySelector('select[name="beginnerColumns"]')?.value || '2');
        const gapValue = parseFloat(manager.querySelector('input[name="beginnerGapValue"]')?.value || '1');
        const gapUnit = manager.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem';

        container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
        container.style.gap = `${gapValue}${gapUnit}`;
        container.style.border = '2px dashed #e5e7eb';
        container.style.borderRadius = '0.375rem';
        container.style.padding = '1rem';

        if (items.length === 0) {
          container.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Нет элементов для отображения</div>';
          return;
        }

        container.innerHTML = items.map((item, index) => {
          let content = 'Пустой элемент';

          if (item.editorData) {
            if (item.type === 'paragraph' && item.editorData.text) {
              content = item.editorData.text.substring(0, 100) + (item.editorData.text.length > 100 ? '...' : '');
            } else if (item.type === 'header' && item.editorData.text) {
              content = `H${item.editorData.level || 1}: ${item.editorData.text.substring(0, 50)}`;
            } else if (item.type === 'image' && item.editorData.file) {
              content = `🖼️ Изображение: ${item.editorData.file.name || 'Без названия'}`;
            } else if (item.type === 'list' && item.editorData.items) {
              content = `📋 Список (${item.editorData.items.length} элементов)`;
            } else {
              content = `📄 ${item.type}`;
            }
          }

          return `
            <div class="grid-preview-item">
              <div class="text-xs text-gray-400 mb-1">Элемент ${index + 1} (${item.type})</div>
              <div class="text-sm">${content}</div>
            </div>
          `;
        }).join('');
      }

      // Функция для рендеринга превью в режиме профи
      function renderProPreview(items, container) {
        const gridTemplateColumns = manager.querySelector('input[name="proGridTemplateColumns"]')?.value || 'repeat(2, 1fr)';
        const gapValue = parseFloat(manager.querySelector('input[name="proGapValue"]')?.value || '1');
        const gapUnit = manager.querySelector('select[name="proGapUnit"]')?.value || 'rem';

        container.style.gridTemplateColumns = gridTemplateColumns;
        container.style.gap = `${gapValue}${gapUnit}`;
        container.style.border = '2px dashed #e5e7eb';
        container.style.borderRadius = '0.375rem';
        container.style.padding = '1rem';

        if (items.length === 0) {
          container.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Нет элементов для отображения</div>';
          return;
        }

        container.innerHTML = items.map((item, index) => {
          let content = 'Пустой элемент';

          if (item.editorData) {
            if (item.type === 'paragraph' && item.editorData.text) {
              content = item.editorData.text.substring(0, 100) + (item.editorData.text.length > 100 ? '...' : '');
            } else if (item.type === 'header' && item.editorData.text) {
              content = `H${item.editorData.level || 1}: ${item.editorData.text.substring(0, 50)}`;
            } else if (item.type === 'image' && item.editorData.file) {
              content = `🖼️ Изображение: ${item.editorData.file.name || 'Без названия'}`;
            } else if (item.type === 'list' && item.editorData.items) {
              content = `📋 Список (${item.editorData.items.length} элементов)`;
            } else {
              content = `📄 ${item.type}`;
            }
          }

          const gridColumnStyle = item.gridColumn ? `grid-column: ${item.gridColumn};` : '';
          const gridRowStyle = item.gridRow ? `grid-row: ${item.gridRow};` : '';
          const combinedStyle = gridColumnStyle + gridRowStyle;

          return `
            <div class="grid-preview-item" ${combinedStyle ? `style="${combinedStyle}"` : ''}>
              <div class="text-xs text-gray-400 mb-1">Элемент ${index + 1} (${item.type})</div>
              <div class="text-sm">${content}</div>
            </div>
          `;
        }).join('');
      }

      // Функция для рендеринга превью в режиме контейнеров
      function renderContainerPreview(container) {
        if (window.GridContainerManager) {
          const containers = window.GridContainerManager.getContainersData();

          if (containers.length === 0) {
            container.innerHTML = `
              <div class="text-center text-blue-600 text-sm py-8">
                <div class="mb-4">
                  <svg class="mx-auto h-12 w-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h4 class="font-medium text-gray-900 mb-2">Режим контейнеров активен</h4>
                <p class="text-gray-600 mb-4">Создайте контейнеры в редакторе для группировки элементов</p>
                <div class="text-xs text-gray-500">
                  <p>1. Нажмите "+" в редакторе</p>
                  <p>2. Выберите "Grid Container"</p>
                  <p>3. Добавляйте элементы в контейнер</p>
                </div>
              </div>
            `;
          } else {
            // Показываем превью контейнеров
            container.innerHTML = containers.map((containerData, index) => {
              const itemsCount = containerData.items ? containerData.items.length : 0;
              const isActive = containerData.isActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-gray-50';

              return `
                <div class="container-preview mb-3 p-3 border-2 rounded-lg ${isActive}">
                  <div class="flex justify-between items-center mb-2">
                    <h5 class="text-sm font-medium ${containerData.isActive ? 'text-blue-700' : 'text-gray-700'}">
                      Контейнер ${index + 1} ${containerData.isActive ? '(активный)' : ''}
                    </h5>
                    <span class="text-xs text-gray-500">${itemsCount} элементов</span>
                  </div>
                  ${itemsCount > 0 ? `
                    <div class="grid grid-cols-2 gap-1">
                      ${containerData.items.slice(0, 4).map(item => `
                        <div class="text-xs p-1 bg-white border rounded">
                          ${item.type}
                        </div>
                      `).join('')}
                      ${itemsCount > 4 ? `<div class="text-xs p-1 bg-gray-100 border rounded text-center">+${itemsCount - 4}</div>` : ''}
                    </div>
                  ` : `
                    <div class="text-xs text-gray-500 text-center py-2">Пустой контейнер</div>
                  `}
                </div>
              `;
            }).join('');
          }
        } else {
          container.innerHTML = `
            <div class="text-center text-red-500 text-sm py-8">
              <p>Grid Container Manager не загружен</p>
            </div>
          `;
        }
      }

      // Обработчики событий (убраны обработчики режимов)
      gridToggle.addEventListener('change', toggleGridSettings);

      // Обработчик изменения настроек
      console.log('🔍 Менеджер:', manager);
      console.log('🔍 ID менеджера:', manager.id);

      const settingsInputs = manager.querySelectorAll('select, input[type="number"], input[type="text"]');
      console.log('🔍 Найдено элементов для отслеживания:', settingsInputs.length);

      // Дополнительная проверка - ищем конкретно beginnerColumns
      const beginnerColumnsSelect = manager.querySelector('select[name="beginnerColumns"]');
      console.log('🔍 beginnerColumns найден:', beginnerColumnsSelect);

      settingsInputs.forEach((input, index) => {
        console.log(`🔍 Элемент ${index + 1}:`, input.name, input.tagName, input.type);
        input.addEventListener('change', function() {
          console.log('🔄 Настройка изменена:', input.name, '=', input.value);
          // Обновляем скрытое поле с данными Grid-системы
          updateGridSystemData();
        });
      });

      // Функция обновления скрытого поля с данными Grid-системы
      function updateGridSystemData() {
        const gridSystemDataField = manager.querySelector('input[name="gridSystemData"]');
        if (gridSystemDataField) {
          const enabled = manager.querySelector('input[name="gridEnabled"]')?.checked || false;

          // Всегда используем режим контейнеров
          const selectedMode = 'container';

          let currentData;

          // Режим контейнеров - получаем данные от менеджера контейнеров
            currentData = {
            enabled: enabled,
            mode: selectedMode,
            containers: window.GridContainerManager ? window.GridContainerManager.getContainersData() : [],
            activeContainerId: window.GridContainerManager ? window.GridContainerManager.activeContainerId : null,
            previewActive: false,
            // Сохраняем базовые настройки для совместимости
            columns: Number(manager.querySelector('select[name="beginnerColumns"]')?.value) || 2,
            gap: {
              value: Number(manager.querySelector('input[name="beginnerGapValue"]')?.value) || 1,
              unit: manager.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem'
            },
            itemAlignment: manager.querySelector('select[name="beginnerItemAlignment"]')?.value || 'stretch',
            contentAlignment: manager.querySelector('select[name="justifyContent"]')?.value || 'start',
            autoRows: manager.querySelector('input[name="autoRows"]')?.value || 'minmax(200px, auto)',
            justifyItems: manager.querySelector('select[name="justifyItems"]')?.value || 'stretch',
            alignContent: manager.querySelector('select[name="alignContent"]')?.value || 'start',
            justifyContent: manager.querySelector('select[name="justifyContent"]')?.value || 'start',
            responsive: {
              desktop: {
                // Сохраняем точное значение из селектора, без fallback
                columns: Number(manager.querySelector('select[name="desktopColumns"]')?.value),
                gap: {
                  value: Number(manager.querySelector('input[name="desktopGapValue"]')?.value),
                  unit: manager.querySelector('select[name="desktopGapUnit"]')?.value
                }
              },
              tablet: {
                columns: Number(manager.querySelector('select[name="tabletColumns"]')?.value) || 2,
                gap: {
                  value: Number(manager.querySelector('input[name="tabletGapValue"]')?.value) || 0.8,
                  unit: manager.querySelector('select[name="tabletGapUnit"]')?.value || 'rem'
                }
              },
              mobile: {
                columns: Number(manager.querySelector('select[name="mobileColumns"]')?.value) || 1,
                gap: {
                  value: Number(manager.querySelector('input[name="mobileGapValue"]')?.value) || 0.5,
                  unit: manager.querySelector('select[name="mobileGapUnit"]')?.value || 'rem'
                }
              }
            },
            items: []
          };

          gridSystemDataField.value = JSON.stringify(currentData);
          console.log('🔄 Обновлены данные Grid-системы:', currentData);
        }
      }

      // Инициализация кнопки превью
      setupGridPreviewButton();

      // Обработчики событий
      gridToggle.addEventListener('change', toggleGridSettings);
      manager.addEventListener('change', updateGridSystemData);

      // Инициальная настройка
      toggleGridSettings();
      initContainerMode(); // Инициализируем режим контейнеров

      // Создаем объект для совместимости с функцией collectGridSystemData
      if (!window.gridSystemManager) {
        window.gridSystemManager = {
          getSettings: function() {
            const gridEnabled = manager.querySelector('input[name="gridEnabled"]')?.checked || false;
            const gridSystemData = manager.querySelector('input[name="gridSystemData"]')?.value || '{}';

            console.log('🔍 CompactGridManager.getSettings():', {
              blockId,
              gridEnabled
            });

            let settings: any = {
              enabled: gridEnabled,
              mode: 'simple',
              items: []
            };

            // Парсим данные элементов
            try {
              const systemData = JSON.parse(gridSystemData);
              Object.assign(settings, systemData);
            } catch (e) {
              console.error('Ошибка парсинга данных Grid-системы:', e);
              // Fallback: собираем данные напрямую из полей формы
              const columnsSelect = manager.querySelector('select[name="beginnerColumns"]') as HTMLSelectElement;
              const gapValueInput = manager.querySelector('input[name="beginnerGapValue"]') as HTMLInputElement;
              const gapUnitSelect = manager.querySelector('select[name="beginnerGapUnit"]') as HTMLSelectElement;
              const itemAlignmentSelect = manager.querySelector('select[name="beginnerItemAlignment"]') as HTMLSelectElement;
              const contentAlignmentSelect = manager.querySelector('select[name="beginnerContentAlignment"]') as HTMLSelectElement;

              settings.columns = Number(columnsSelect?.value) || 2;
              settings.gap = {
                value: Number(gapValueInput?.value) || 1,
                unit: gapUnitSelect?.value || 'rem'
              };
              settings.itemAlignment = itemAlignmentSelect?.value || 'stretch';
              settings.contentAlignment = contentAlignmentSelect?.value || 'start';
            }

            console.log('🔍 Собраны настройки Grid:', settings);
            return settings;
          }
        };
      }
    });
  });
</script>
