import fs from 'fs/promises';
import path from 'path';
import { validateCallRequest } from '../../../utils/validation.js';

const ordersPath = path.join(process.cwd(), 'data/orders/orders-call.json');

export async function POST({ request }) {
  try {
    const body = await request.json();

    // Валидация данных запроса на звонок
    const validationErrors = validateCallRequest(body);
    if (validationErrors.length > 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Ошибки валидации данных',
        details: validationErrors
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создание объекта запроса на звонок
    const order = {
      id: generateCallId(),
      type: 'call',
      name: body.name.trim(),
      phone: body.phone,
      message: body.message ? body.message.trim() : '',
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Чтение существующих запросов
    let ordersData;
    try {
      const data = await fs.readFile(ordersPath, 'utf-8');
      ordersData = JSON.parse(data);
    } catch (error) {
      // Если файл не существует, создаем новую структуру
      ordersData = { orders: [] };
    }

    // Добавление нового запроса
    ordersData.orders.unshift(order); // Добавляем в начало для сортировки по дате

    // Сохранение в файл
    await fs.writeFile(ordersPath, JSON.stringify(ordersData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ 
      success: true, 
      orderId: order.id,
      message: 'Запрос на звонок успешно отправлен' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при создании запроса на звонок:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при отправке запроса' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция генерации ID запроса на звонок
function generateCallId() {
  // 7 случайных цифр
  const part1 = Array.from({length: 7}, () => Math.floor(Math.random() * 10)).join('');
  // 3 случайных цифры
  const part2 = Array.from({length: 3}, () => Math.floor(Math.random() * 10)).join('');
  return `CALL-${part1}-${part2}`;
}
