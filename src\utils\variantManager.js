/**
 * Утилиты для управления вариантами товаров
 */

// Счетчик для генерации уникальных ID вариантов
let variantCounter = 0;

/**
 * Инициализация компонента вариантов
 */
export function initializeVariantsComponent() {
  const addVariantBtn = document.getElementById('add-variant-btn');
  if (addVariantBtn) {
    addVariantBtn.addEventListener('click', addVariantRow);
  }
}

/**
 * Добавление нового варианта товара
 */
export function addVariantRow() {
  const container = document.getElementById('variants-container');
  if (!container) return;

  const variantId = `variant-${++variantCounter}`;
  const variantRow = document.createElement('div');
  variantRow.className = 'variant-row bg-white p-6 rounded-lg border border-gray-200 shadow-sm';
  variantRow.id = variantId;
  // Сохраняем ID варианта в data-атрибуте для последующего использования
  variantRow.setAttribute('data-variant-id', null); // Будет установлен при сохранении

  variantRow.innerHTML = `

    <!-- Сохраненный вид (скрыт по умолчанию) -->
    <div class="variant-saved-view hidden">
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex-1">
          <div class="flex items-center gap-2 mb-1">
            <h4 class="text-lg font-semibold text-gray-900 variant-saved-name">Вариант ${variantCounter}</h4>
            <span class="variant-primary-price-badge hidden px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Основная цена</span>
          </div>
          <p class="text-sm text-gray-600">
            <span class="variant-saved-price">0.00</span> <span class="variant-saved-currency">BYN</span>
            <span class="variant-saved-attributes ml-2 text-gray-500"></span>
          </p>
        </div>
        <div class="flex items-center gap-2">
          <button type="button" class="edit-variant-btn inline-flex items-center h-8 px-3 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Редактировать
          </button>
          <button type="button" class="remove-variant-btn inline-flex items-center justify-center w-8 h-8 border border-red-300 text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-variant-id="${variantId}" title="Удалить вариант">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Форма редактирования -->
    <div class="variant-edit-form">
      <!-- Заголовок варианта в режиме редактирования -->
      <div class="variant-header flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
        <div class="flex items-center gap-3">
          <h4 class="text-lg font-semibold text-gray-900">Вариант ${variantCounter}</h4>
          <span class="variant-status-badge px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Редактирование</span>
        </div>
        <div class="flex items-center gap-2">
          <button type="button" class="save-variant-btn inline-flex items-center h-8 px-3 border border-green-300 text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Сохранить
          </button>
          <button type="button" class="remove-variant-btn inline-flex items-center justify-center w-8 h-8 border border-red-300 text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors" data-variant-id="${variantId}" title="Удалить вариант">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
      <div class="space-y-6">
        <!-- Блок атрибутов варианта (во всю ширину) -->
        <div class="variant-attributes">
          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h5 class="text-sm font-semibold text-gray-900 mb-4">Атрибуты варианта</h5>
            <div class="variant-attributes-container space-y-0 mb-4" data-variant-id="${variantId}">
              <!-- Атрибуты варианта будут добавлены динамически -->
            </div>
            <button type="button" class="add-variant-attribute-btn w-full px-4 py-3 border border-dashed border-gray-300 text-sm font-medium rounded-md text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50 transition-colors" data-variant-id="${variantId}">
              <svg class="w-4 h-4 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Добавить атрибут
            </button>
          </div>
        </div>

        <!-- Блок основной информации -->
        <div class="variant-pricing">
          <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h5 class="text-sm font-semibold text-gray-900 mb-4">Основная информация</h5>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <!-- Название варианта и SKU в одной строке -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">Название варианта</label>
                <input type="text" class="variant-name w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Например: Красный, размер M">
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">SKU (артикул)</label>
                <div class="flex gap-2">
                  <input type="text" class="variant-sku flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Автогенерация">
                  <button type="button" class="generate-sku-btn p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-blue-300 rounded-md transition-colors" title="Генерировать SKU">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Цена -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
                <input type="number" step="0.01" class="variant-price w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                <div class="mt-2">
                  <label class="flex items-center">
                    <input type="checkbox" class="variant-is-primary-price h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    <span class="ml-2 text-xs text-gray-600">Использовать как основную цену товара</span>
                  </label>
                  <p class="text-xs text-gray-500 mt-1">Эта цена будет отображаться в карточке товара и использоваться для фильтрации</p>
                </div>
              </div>

              <!-- Валюта (только символ) -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Валюта</label>
                <select class="variant-currency w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <!-- Валюты будут заполнены динамически -->
                </select>
              </div>

              <!-- Единица измерения -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">Единица измерения</label>
                <select class="variant-unit w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <!-- Единицы будут заполнены динамически -->
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(variantRow);



  // Инициализируем валюты и единицы для нового варианта
  initializeVariantCurrenciesAndUnits(variantRow);

  // Добавляем обработчики событий
  const removeBtn = variantRow.querySelector('.remove-variant-btn');
  const addAttributeBtn = variantRow.querySelector('.add-variant-attribute-btn');
  const saveBtn = variantRow.querySelector('.save-variant-btn');
  const editBtn = variantRow.querySelector('.edit-variant-btn');

  if (removeBtn) {
    removeBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      event.preventDefault();
      removeVariantRow(this.dataset.variantId);
    });
  }

  if (addAttributeBtn) {
    addAttributeBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      addVariantAttributeRow(this.dataset.variantId);
    });
  }

  if (saveBtn) {
    saveBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      saveVariant(variantId);
    });
  }

  if (editBtn) {
    editBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      editVariant(variantId);
    });
  }

  // Обработчик для кнопки генерации SKU
  const generateSkuBtn = variantRow.querySelector('.generate-sku-btn');
  if (generateSkuBtn) {
    generateSkuBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      generateVariantSKU(variantId);
    });
  }

  // Добавляем обработчики для полей цены, валюты и единицы измерения
  const priceInput = variantRow.querySelector('.variant-price');
  const currencySelect = variantRow.querySelector('.variant-currency');
  const unitSelect = variantRow.querySelector('.variant-unit');

  if (priceInput) {
    priceInput.addEventListener('input', function() {
      toggleBasePriceFields();
      notifyVariantChange();
    });
    priceInput.addEventListener('change', function() {
      toggleBasePriceFields();
      notifyVariantChange();
    });
  }

  if (currencySelect) {
    currencySelect.addEventListener('change', function() {
      toggleBasePriceFields();
      notifyVariantChange();
    });
  }

  if (unitSelect) {
    unitSelect.addEventListener('change', function() {
      toggleBasePriceFields();
      notifyVariantChange();
    });
  }

  // Добавляем обработчик для чекбокса "Основная цена"
  const isPrimaryPriceCheckbox = variantRow.querySelector('.variant-is-primary-price');
  if (isPrimaryPriceCheckbox) {
    isPrimaryPriceCheckbox.addEventListener('change', function() {
      handlePrimaryPriceChange(this, variantId);
    });
  }
}

// Список слушателей изменений вариантов
let variantChangeListeners = [];

/**
 * Добавляет слушатель изменений вариантов
 */
window.addVariantChangeListener = function(listener) {
  if (typeof listener === 'function') {
    variantChangeListeners.push(listener);
  }
};

/**
 * Получает актуальные данные всех вариантов из DOM
 */
window.getProductVariants = function() {
  const variants = [];
  const variantRows = document.querySelectorAll('.variant-row');

  variantRows.forEach((row, index) => {
    try {
      // Проверяем, находится ли вариант в режиме редактирования
      const editForm = row.querySelector('.variant-edit-form');
      const isInEditMode = editForm && !editForm.classList.contains('hidden');

      let variant = null;

      if (isInEditMode) {
        // Если в режиме редактирования, собираем данные из DOM
        const nameInput = row.querySelector('.variant-name');
        const priceInput = row.querySelector('.variant-price');
        const currencySelect = row.querySelector('.variant-currency');
        const unitSelect = row.querySelector('.variant-unit');
        const isPrimaryCheckbox = row.querySelector('.variant-is-primary-price');
        const skuInput = row.querySelector('.variant-sku');

        // Получаем атрибуты из сохраненных данных
        let savedAttributes = [];
        const variantDataStr = row.dataset.variantData;
        if (variantDataStr) {
          try {
            const savedData = JSON.parse(variantDataStr);
            savedAttributes = savedData.attributes || [];
          } catch (error) {
            // Игнорируем ошибки парсинга
          }
        }

        if (nameInput && priceInput && currencySelect && unitSelect) {
          variant = {
            name: nameInput.value,
            price: {
              value: parseFloat(priceInput.value) || 0,
              currency: currencySelect.value,
              unit: unitSelect.value
            },
            sku: skuInput ? skuInput.value : '',
            isPrimaryPrice: isPrimaryCheckbox ? isPrimaryCheckbox.checked : false,
            attributes: savedAttributes
          };
        }
      } else {
        // Если вариант свернут, используем сохраненные данные
        const variantDataStr = row.dataset.variantData;
        if (variantDataStr) {
          try {
            const savedData = JSON.parse(variantDataStr);
            variant = {
              name: savedData.name || '',
              price: savedData.price || { value: 0, currency: '', unit: '' },
              sku: savedData.sku || '',
              isPrimaryPrice: savedData.isPrimaryPrice || false,
              attributes: savedData.attributes || []
            };
          } catch (error) {
            // Игнорируем ошибки парсинга
          }
        }
      }

      if (variant && variant.price && variant.price.value > 0) {
        variants.push(variant);
      }
    } catch (error) {
      // Игнорируем ошибки обработки варианта
    }
  });

  return variants;
};

/**
 * Уведомляет всех слушателей об изменениях в вариантах
 */
function notifyVariantChange() {
  // Добавляем небольшую задержку, чтобы DOM успел обновиться
  setTimeout(() => {
    variantChangeListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        // Игнорируем ошибки в слушателях
      }
    });
  }, 10);
}

/**
 * Обработка изменения чекбокса "Основная цена"
 */
function handlePrimaryPriceChange(checkbox, currentVariantId) {


  if (checkbox.checked) {
    // Если текущий чекбокс отмечен, снимаем отметку со всех остальных
    const allVariantRows = document.querySelectorAll('.variant-row');
    allVariantRows.forEach(row => {
      if (row.id !== currentVariantId) {
        // Снимаем галочку с чекбокса в DOM (для всех вариантов)
        const otherCheckbox = row.querySelector('.variant-is-primary-price');
        if (otherCheckbox) {
          otherCheckbox.checked = false;

        }

        // Обновляем сохраненные данные в dataset
        const variantDataStr = row.dataset.variantData;
        if (variantDataStr) {
          try {
            const variantData = JSON.parse(variantDataStr);
            variantData.isPrimaryPrice = false;
            row.dataset.variantData = JSON.stringify(variantData);

            // Обновляем отображение бейджа "Основная цена"
            const primaryPriceBadge = row.querySelector('.variant-primary-price-badge');
            if (primaryPriceBadge) {
              primaryPriceBadge.classList.add('hidden');
            }

          } catch (error) {
            // Игнорируем ошибки обновления данных
          }
        }
      }
    });
  }

  // Обновляем сохраненные данные для текущего варианта
  updateVariantDataInDataset(currentVariantId);

  // Если текущий вариант помечен как основная цена, обновляем его бейдж
  if (checkbox.checked) {
    const currentRow = document.getElementById(currentVariantId);
    if (currentRow) {
      const primaryPriceBadge = currentRow.querySelector('.variant-primary-price-badge');
      if (primaryPriceBadge) {
        primaryPriceBadge.classList.remove('hidden');
      }
    }
  }

  // Обновляем состояние полей базовой цены
  toggleBasePriceFields();

  // Уведомляем об изменении
  notifyVariantChange();
}

/**
 * Обновляет сохраненные данные варианта в dataset
 */
function updateVariantDataInDataset(variantId) {
  const row = document.getElementById(variantId);
  if (!row) return;

  try {
    // Получаем существующие данные для сохранения атрибутов
    const existingDataStr = row.dataset.variantData;
    let existingData = {};
    if (existingDataStr) {
      try {
        existingData = JSON.parse(existingDataStr);
      } catch (error) {
        // Игнорируем ошибки парсинга
      }
    }

    const nameInput = row.querySelector('.variant-name');
    const priceInput = row.querySelector('.variant-price');
    const currencySelect = row.querySelector('.variant-currency');
    const unitSelect = row.querySelector('.variant-unit');
    const skuInput = row.querySelector('.variant-sku');
    const isPrimaryCheckbox = row.querySelector('.variant-is-primary-price');

    if (nameInput && priceInput && currencySelect && unitSelect) {
      const variantData = {
        name: nameInput.value,
        price: {
          value: parseFloat(priceInput.value) || 0,
          currency: currencySelect.value,
          unit: unitSelect.value
        },
        sku: skuInput ? skuInput.value : '',
        isPrimaryPrice: isPrimaryCheckbox ? isPrimaryCheckbox.checked : false,
        attributes: existingData.attributes || []
      };

      // Проверяем, находится ли вариант в режиме редактирования для обновления атрибутов
      const editForm = row.querySelector('.variant-edit-form');
      const isInEditMode = editForm && !editForm.classList.contains('hidden');

      if (isInEditMode) {
        // Если в режиме редактирования, собираем атрибуты из DOM
        variantData.attributes = [];
        const attributeRows = row.querySelectorAll('.variant-attribute-row');
        attributeRows.forEach(attrRow => {
          const typeSelect = attrRow.querySelector('.variant-attribute-type');
          const valueSelect = attrRow.querySelector('.variant-attribute-value-select');
          const valueInput = attrRow.querySelector('.variant-attribute-value-input');
          const valueCheckboxes = attrRow.querySelectorAll('.variant-attribute-checkbox:checked');

          if (typeSelect && typeSelect.value) {
            const attribute = {
              type: typeSelect.value,
              value: null
            };

            if (valueSelect && valueSelect.value) {
              attribute.value = valueSelect.value;
            } else if (valueInput && valueInput.value) {
              attribute.value = valueInput.value;
            } else if (valueCheckboxes.length > 0) {
              attribute.value = Array.from(valueCheckboxes).map(cb => cb.value);
            }

            if (attribute.value !== null) {
              variantData.attributes.push(attribute);
            }
          }
        });
      }

      row.dataset.variantData = JSON.stringify(variantData);
    }
  } catch (error) {
    // Игнорируем ошибки обновления данных
  }
}

/**
 * Удаление варианта товара
 */
export function removeVariantRow(variantId) {
  const row = document.getElementById(variantId);
  if (row) {
    row.remove();
    // Обновляем состояние полей базовой цены после удаления варианта
    toggleBasePriceFields();
    // Уведомляем об изменении
    notifyVariantChange();
  }
}

/**
 * Инициализация валют и единиц измерения для варианта
 */
export function initializeVariantCurrenciesAndUnits(variantRow) {
  const currencySelect = variantRow.querySelector('.variant-currency');
  const unitSelect = variantRow.querySelector('.variant-unit');

  // Заполняем валюты
  if (currencySelect && window.variantSettingsData?.currencies?.supported) {
    window.variantSettingsData.currencies.supported.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.key;
      option.textContent = currency.simvol; // Показываем только символ
      option.dataset.symbol = currency.simvol;
      option.dataset.label = currency.label.ru;
      if (currency.key === window.variantSettingsData.currencies.primary) {
        option.selected = true;
      }
      currencySelect.appendChild(option);
    });
  }

  // Заполняем единицы измерения из основной формы товара
  if (unitSelect) {
    const mainUnitSelect = document.getElementById('unit');
    if (mainUnitSelect && mainUnitSelect.options) {
      // Копируем опции из основного селекта единиц
      Array.from(mainUnitSelect.options).forEach(option => {
        if (option.value) { // Пропускаем пустые опции
          const newOption = document.createElement('option');
          newOption.value = option.value;
          newOption.textContent = option.textContent;
          newOption.dataset.unitLabel = option.dataset.unitLabel;
          unitSelect.appendChild(newOption);
        }
      });

      // Выбираем ту же единицу, что и в основной форме
      if (mainUnitSelect.value) {
        unitSelect.value = mainUnitSelect.value;
      }
    }
  }
}



/**
 * Генерация SKU для варианта
 */
export function generateVariantSKU(variantId) {
  const variantRow = document.getElementById(variantId);
  if (!variantRow) return;

  const skuInput = variantRow.querySelector('.variant-sku');
  if (!skuInput) return;

  // Получаем ID товара из URL или формы
  let productId = '';
  const urlParts = window.location.pathname.split('/');
  if (urlParts.includes('edit')) {
    // Для редактирования берем ID из URL
    productId = urlParts[urlParts.length - 1];
  } else {
    // Для новых товаров пытаемся получить ID из формы
    const idInput = document.getElementById('id');
    if (idInput && idInput.value) {
      productId = idInput.value;
    } else {
      // Если ID нет, генерируем временный
      productId = 'NEW';
    }
  }



  // Получаем все существующие варианты для подсчета
  const allVariantRows = document.querySelectorAll('.variant-row');
  let variantNumber = 1;

  // Находим максимальный номер среди существующих SKU
  allVariantRows.forEach(row => {
    const existingSku = row.querySelector('.variant-sku')?.value;
    if (existingSku && existingSku.startsWith(productId + '-')) {
      const parts = existingSku.split('-');
      if (parts.length >= 2) {
        const numberPart = parts[parts.length - 1]; // Берем последнюю часть
        if (numberPart && /^\d{2}$/.test(numberPart)) {
          const num = parseInt(numberPart, 10);
          if (num >= variantNumber) {
            variantNumber = num + 1;
          }
        }
      }
    }
  });

  // Формируем финальный SKU с двузначным номером
  const sku = `${productId}-${variantNumber.toString().padStart(2, '0')}`;

  skuInput.value = sku;
}

/**
 * Добавление атрибута к варианту (новый UI с возможностью сворачивания)
 */
export function addVariantAttributeRow(variantId) {
  const variantRow = document.getElementById(variantId);
  if (!variantRow) {
    return;
  }

  const container = variantRow.querySelector('.variant-attributes-container');
  if (!container) {
    return;
  }
  // Унификация: всегда применяем space-y-4 для отступов между атрибутами
  container.classList.add('space-y-4');

  // Получаем выбранные атрибуты из основной формы
  const selectedAttributes = window.getSelectedProductAttributes();

  if (Object.keys(selectedAttributes).length === 0) {
    alert('Сначала добавьте атрибуты в основную форму товара');
    return;
  }

  const attributeId = `variant-attr-${variantId}-${Date.now()}`;
  const attributeRow = document.createElement('div');
  // Унификация: убираем mb-3, оставляем только базовые стили
  attributeRow.className = 'variant-attribute-row bg-white border border-gray-200 rounded-lg';
  attributeRow.id = attributeId;

  // Создаем опции только для выбранных атрибутов
  const attributeOptions = Object.entries(selectedAttributes).map(([key, attr]) =>
    `<option value="${key}">${attr.name}</option>`
  ).join('');

  attributeRow.innerHTML = `
    <!-- Развернутая форма -->
    <div class="attribute-form-expanded p-4">
      <div class="flex justify-between items-center mb-4">
        <h6 class="text-sm font-semibold text-gray-900">Новый атрибут</h6>
        <div class="flex items-center gap-2">
          <button type="button" class="collapse-attribute-btn px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
            Сохранить
          </button>
          <button type="button" class="remove-variant-attribute-btn px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 border border-red-300 rounded-md transition-colors" data-attribute-id="${attributeId}">
            Удалить
          </button>
        </div>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Тип атрибута</label>
          <select class="variant-attribute-type w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Выберите атрибут</option>
            ${attributeOptions}
          </select>
        </div>

        <div class="variant-attribute-value">
          <label class="block text-sm font-medium text-gray-700 mb-2">Значение</label>
          <div class="px-3 py-2 text-sm text-gray-500 bg-gray-50 border border-gray-300 rounded-md">
            Выберите тип атрибута
          </div>
        </div>
      </div>
    </div>

    <!-- Свернутый вид (скрыт по умолчанию) -->
    <div class="attribute-form-collapsed hidden p-3 bg-gray-50">
      <div class="flex justify-between items-center">
        <div class="flex-1">
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-gray-900 attribute-type-name">Тип атрибута</span>
            <span class="text-xs text-gray-500">•</span>
            <span class="text-sm text-gray-700 attribute-value-summary">Значение</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button type="button" class="expand-attribute-btn text-sm text-blue-600 hover:text-blue-800 font-medium">
            Редактировать
          </button>
          <button type="button" class="remove-variant-attribute-btn text-red-600 hover:text-red-800 hover:bg-red-50 p-1 rounded transition-colors" data-attribute-id="${attributeId}" title="Удалить атрибут">
            ×
          </button>
        </div>
      </div>
    </div>
  `;

  container.appendChild(attributeRow);



  // Добавляем обработчики событий
  const typeSelect = attributeRow.querySelector('.variant-attribute-type');
  const removeBtns = attributeRow.querySelectorAll('.remove-variant-attribute-btn');
  const collapseBtn = attributeRow.querySelector('.collapse-attribute-btn');
  const expandBtn = attributeRow.querySelector('.expand-attribute-btn');

  if (typeSelect) {
    typeSelect.addEventListener('click', function(event) {
      event.stopPropagation();
    });

    typeSelect.addEventListener('change', function(event) {
      event.stopPropagation();
      handleVariantAttributeTypeChange(this, attributeId);
      // Активируем кнопку сохранения после выбора типа
      updateCollapseButtonState(attributeId);
    });
  }

  // Обработчики для кнопок удаления (есть в обоих видах)
  removeBtns.forEach(btn => {
    btn.addEventListener('click', function(event) {
      event.stopPropagation(); // Предотвращаем всплытие события
      removeVariantAttributeRow(this.dataset.attributeId);
    });
  });

  // Обработчик сохранения (сворачивания)
  if (collapseBtn) {
    collapseBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      collapseAttributeForm(attributeId);
    });
  }

  // Обработчик разворачивания
  if (expandBtn) {
    expandBtn.addEventListener('click', function(event) {
      event.stopPropagation();
      expandAttributeForm(attributeId);
    });
  }
}

/**
 * Удаление атрибута варианта
 */
export function removeVariantAttributeRow(attributeId) {
  const row = document.getElementById(attributeId);
  if (row) {
    row.remove();
  }
}



/**
 * Обновление состояния кнопки сохранения
 */
export function updateCollapseButtonState(attributeId) {
  const attributeRow = document.getElementById(attributeId);
  if (!attributeRow) {
    return;
  }

  const typeSelect = attributeRow.querySelector('.variant-attribute-type');
  const valueContainer = attributeRow.querySelector('.variant-attribute-value');
  const collapseBtn = attributeRow.querySelector('.collapse-attribute-btn');

  if (!typeSelect || !collapseBtn) {
    return;
  }

  // Проверяем, выбран ли тип атрибута и есть ли значение
  const hasType = typeSelect.value !== '';
  let hasValue = false;

  if (valueContainer) {
    const valueSelect = valueContainer.querySelector('.variant-attribute-value-select');
    const checkedBoxes = valueContainer.querySelectorAll('.variant-attribute-checkbox:checked');
    const hiddenInput = valueContainer.querySelector('.variant-attribute-value-input');

    hasValue = (valueSelect && valueSelect.value !== '') ||
               (checkedBoxes && checkedBoxes.length > 0) ||
               (hiddenInput && hiddenInput.value !== '');

  }

  // Активируем кнопку только если выбран тип и есть значение
  const shouldEnable = hasType && hasValue;
  collapseBtn.disabled = !shouldEnable;
}

/**
 * Сворачивание формы атрибута
 */
export function collapseAttributeForm(attributeId) {
  const attributeRow = document.getElementById(attributeId);
  if (!attributeRow) return;

  const expandedForm = attributeRow.querySelector('.attribute-form-expanded');
  const collapsedForm = attributeRow.querySelector('.attribute-form-collapsed');
  const typeSelect = attributeRow.querySelector('.variant-attribute-type');
  const valueContainer = attributeRow.querySelector('.variant-attribute-value');

  if (!expandedForm || !collapsedForm || !typeSelect) return;

  // Получаем данные для отображения в свернутом виде
  const selectedOption = typeSelect.options[typeSelect.selectedIndex];
  const typeName = selectedOption ? selectedOption.text : 'Тип атрибута';

  // Получаем краткое описание значения
  let valueSummary = 'Значение не выбрано';

  const valueSelect = valueContainer.querySelector('.variant-attribute-value-select');
  const checkedBoxes = valueContainer.querySelectorAll('.variant-attribute-checkbox:checked');
  const hiddenInput = valueContainer.querySelector('.variant-attribute-value-input');

  if (valueSelect && valueSelect.value) {
    const selectedValueOption = valueSelect.options[valueSelect.selectedIndex];
    valueSummary = selectedValueOption ? selectedValueOption.text : valueSelect.value;
  } else if (checkedBoxes.length > 0) {
    if (checkedBoxes.length === 1) {
      valueSummary = checkedBoxes[0].nextElementSibling?.textContent || checkedBoxes[0].value;
    } else {
      valueSummary = `${checkedBoxes.length} выбрано`;
    }
  } else if (hiddenInput && hiddenInput.value) {
    // Для объектов типа surfaces, patterns
    const parentDiv = hiddenInput.closest('div');
    const nameSpan = parentDiv?.querySelector('.font-medium');
    valueSummary = nameSpan ? nameSpan.textContent : hiddenInput.value;
  }

  // Обновляем текст в свернутом виде
  const typeNameElement = collapsedForm.querySelector('.attribute-type-name');
  const valueSummaryElement = collapsedForm.querySelector('.attribute-value-summary');

  if (typeNameElement) typeNameElement.textContent = typeName;
  if (valueSummaryElement) valueSummaryElement.textContent = valueSummary;

  // Переключаем видимость
  expandedForm.classList.add('hidden');
  collapsedForm.classList.remove('hidden');
}

/**
 * Разворачивание формы атрибута
 */
export function expandAttributeForm(attributeId) {
  const attributeRow = document.getElementById(attributeId);
  if (!attributeRow) return;

  const expandedForm = attributeRow.querySelector('.attribute-form-expanded');
  const collapsedForm = attributeRow.querySelector('.attribute-form-collapsed');

  if (!expandedForm || !collapsedForm) return;

  // Переключаем видимость
  collapsedForm.classList.add('hidden');
  expandedForm.classList.remove('hidden');
}

/**
 * Обработка изменения типа атрибута в варианте
 */
export function handleVariantAttributeTypeChange(selectElement, attributeId) {
  try {
    const attributeType = selectElement.value;
    const row = document.getElementById(attributeId);

    if (!row || !attributeType) {
      return;
    }

    const valueContainer = row.querySelector('.variant-attribute-value');

    if (!valueContainer) {
      return;
    }

    // Получаем выбранные атрибуты из основной формы
    const selectedAttributes = window.getSelectedProductAttributes();
    const selectedAttribute = selectedAttributes[attributeType];

    if (!selectedAttribute) {
      valueContainer.innerHTML = '<p class="text-xs text-red-500 py-1">Атрибут не найден в основной форме</p>';
      return;
    }

    // Получаем доступные значения для выбранного атрибута
    const availableValues = selectedAttribute.value;
    const typeConfig = window.attributeTypesFullConfig?.[attributeType];

    if (!availableValues) {
      valueContainer.innerHTML = '<p class="text-xs text-red-500 py-1">Нет доступных значений</p>';
      return;
    }

  // Создаем упрощенный интерфейс для выбора значений атрибутов в вариантах
  // Проверяем, разрешен ли множественный выбор для этого типа атрибута
  const allowMultiple = typeConfig?.allowMultipleInVariants === true;

  if (typeConfig.isSimpleArray) {
    // Простой массив строк
    if (allowMultiple) {
      // Множественный выбор через чекбоксы
      valueContainer.innerHTML = `
        <div class="space-y-2 max-h-32 overflow-y-auto">
          ${availableValues.map(item => `
            <label class="flex items-center">
              <input type="checkbox" class="variant-attribute-checkbox mr-2" value="${item}">
              <span class="text-sm">${item}</span>
            </label>
          `).join('')}
        </div>
      `;
    } else {
      // Единичный выбор через select
      valueContainer.innerHTML = `
        <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${availableValues.map(item => `<option value="${item}">${item}</option>`).join('')}
        </select>
      `;
    }
  } else if (attributeType === 'colors') {
    // Цвета - availableValues содержит массив строк (названий цветов)
    if (allowMultiple) {
      // Множественный выбор цветов через чекбоксы
      valueContainer.innerHTML = `
        <div class="space-y-2 max-h-32 overflow-y-auto">
          ${availableValues.map(colorName => `
            <label class="flex items-center">
              <input type="checkbox" class="variant-attribute-checkbox mr-2" value="${colorName}">
              <span class="text-sm">${colorName}</span>
            </label>
          `).join('')}
        </div>
        <p class="text-xs text-gray-500 mt-2">Выберите несколько цветов для многоцветного изделия</p>
      `;
    } else {
      // Единичный выбор цвета через select
      valueContainer.innerHTML = `
        <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите цвет</option>
          ${availableValues.map(colorName => `<option value="${colorName}">${colorName}</option>`).join('')}
        </select>
      `;
    }
  } else if (attributeType === 'size') {
    // Размеры - availableValues содержит массив выбранных размеров
    valueContainer.innerHTML = `
      <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        <option value="">Выберите размер</option>
        ${availableValues.map(size => `<option value='${JSON.stringify(size).replace(/'/g, "&apos;")}'>${
          size.length ? `${size.length}×${size.width}×${size.height} мм` : size.name || JSON.stringify(size)
        }</option>`).join('')}
      </select>
    `;
  } else if (['surfaces', 'patterns'].includes(attributeType)) {
    // Объекты с id, name, description - availableValues содержит один объект
    if (availableValues && availableValues.id) {
      valueContainer.innerHTML = `
        <div class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md">
          <span class="font-medium text-blue-900">${availableValues.name}</span>
          ${availableValues.description ? `<p class="text-xs text-blue-700 mt-1">${availableValues.description}</p>` : ''}
          <input type="hidden" class="variant-attribute-value-input" value="${availableValues.id}">
        </div>
      `;
    } else {
      valueContainer.innerHTML = '<div class="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">Значение не выбрано</div>';
    }
  } else if (attributeType === 'color_pigments') {
    // Цветовые пигменты
    let pigmentOptions = [];

    if (Array.isArray(availableValues)) {
      pigmentOptions = availableValues;
    } else if (availableValues && availableValues.id) {
      pigmentOptions = [availableValues];
    }

    if (pigmentOptions.length > 0) {
      if (allowMultiple) {
        // Множественный выбор пигментов через чекбоксы
        valueContainer.innerHTML = `
          <div class="space-y-2 max-h-32 overflow-y-auto">
            ${pigmentOptions.map(pigment => `
              <label class="flex items-start">
                <input type="checkbox" class="variant-attribute-checkbox mr-2 mt-1" value="${pigment.id}" data-name="${pigment.name}" data-description="${pigment.description || ''}">
                <div class="flex-1">
                  <span class="text-sm font-medium">${pigment.name}</span>
                  ${pigment.description ? `<p class="text-xs text-gray-600 mt-1">${pigment.description}</p>` : ''}
                </div>
              </label>
            `).join('')}
          </div>
          <p class="text-xs text-gray-500 mt-2">Выберите несколько пигментов для комбинированной окраски</p>
        `;
      } else {
        // Единичный выбор пигмента через select
        const optionsHtml = pigmentOptions.map(pigment => {
          return `<option value="${pigment.id}" data-name="${pigment.name}" data-description="${pigment.description || ''}">${pigment.name}</option>`;
        }).join('');

        valueContainer.innerHTML = `
          <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Выберите пигмент</option>
            ${optionsHtml}
          </select>
        `;
      }
    } else {
      valueContainer.innerHTML = '<div class="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">Пигменты не найдены</div>';
    }
  } else if (attributeType === 'material') {
    // Материалы - availableValues может быть массивом или объектом
    let materialOptions = [];

    if (Array.isArray(availableValues)) {
      materialOptions = availableValues;
    } else if (availableValues && availableValues.id) {
      materialOptions = [availableValues];
    }

    if (materialOptions.length > 0) {
      if (allowMultiple) {
        // Множественный выбор материалов через чекбоксы
        valueContainer.innerHTML = `
          <div class="space-y-2 max-h-32 overflow-y-auto">
            ${materialOptions.map(material => `
              <label class="flex items-start">
                <input type="checkbox" class="variant-attribute-checkbox mr-2 mt-1" value="${material.id}" data-name="${material.name}" data-description="${material.description || ''}">
                <div class="flex-1">
                  <span class="text-sm font-medium">${material.name}</span>
                  ${material.description ? `<p class="text-xs text-gray-600 mt-1">${material.description}</p>` : ''}
                </div>
              </label>
            `).join('')}
          </div>
          <p class="text-xs text-gray-500 mt-2">Выберите несколько материалов для комбинированного варианта</p>
        `;
      } else {
        // Единичный выбор материала через select
        valueContainer.innerHTML = `
          <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Выберите материал</option>
            ${materialOptions.map(material => `<option value="${material.id}">${material.name}</option>`).join('')}
          </select>
        `;
      }
    } else {
      valueContainer.innerHTML = '<div class="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">Значение не выбрано</div>';
    }
  } else {
    // Другие типы - проверяем, есть ли доступные значения для выбора
    if (availableValues && (Array.isArray(availableValues) || typeof availableValues === 'object')) {
      // Есть доступные значения - создаем интерфейс выбора
      if (allowMultiple) {
        // Множественный выбор через чекбоксы
        let checkboxesHtml = '';

        if (Array.isArray(availableValues)) {
          checkboxesHtml = availableValues.map(item => {
            if (typeof item === 'object' && item.id && item.name) {
              return `
                <label class="flex items-start">
                  <input type="checkbox" class="variant-attribute-checkbox mr-2 mt-1" value="${item.id}" data-name="${item.name}" data-description="${item.description || ''}">
                  <div class="flex-1">
                    <span class="text-sm font-medium">${item.name}</span>
                    ${item.description ? `<p class="text-xs text-gray-600 mt-1">${item.description}</p>` : ''}
                  </div>
                </label>
              `;
            } else if (typeof item === 'string') {
              return `
                <label class="flex items-center">
                  <input type="checkbox" class="variant-attribute-checkbox mr-2" value="${item}">
                  <span class="text-sm">${item}</span>
                </label>
              `;
            } else {
              const itemStr = JSON.stringify(item);
              return `
                <label class="flex items-center">
                  <input type="checkbox" class="variant-attribute-checkbox mr-2" value="${itemStr}">
                  <span class="text-sm">${itemStr}</span>
                </label>
              `;
            }
          }).join('');
        } else if (availableValues && typeof availableValues === 'object') {
          if (availableValues.id && availableValues.name) {
            checkboxesHtml = `
              <label class="flex items-start">
                <input type="checkbox" class="variant-attribute-checkbox mr-2 mt-1" value="${availableValues.id}" data-name="${availableValues.name}" data-description="${availableValues.description || ''}">
                <div class="flex-1">
                  <span class="text-sm font-medium">${availableValues.name}</span>
                  ${availableValues.description ? `<p class="text-xs text-gray-600 mt-1">${availableValues.description}</p>` : ''}
                </div>
              </label>
            `;
          } else {
            const itemStr = JSON.stringify(availableValues);
            checkboxesHtml = `
              <label class="flex items-center">
                <input type="checkbox" class="variant-attribute-checkbox mr-2" value="${itemStr}">
                <span class="text-sm">${itemStr}</span>
              </label>
            `;
          }
        }

        if (checkboxesHtml) {
          valueContainer.innerHTML = `
            <div class="space-y-2 max-h-32 overflow-y-auto">
              ${checkboxesHtml}
            </div>
            <p class="text-xs text-gray-500 mt-2">Выберите несколько значений для комбинированного варианта</p>
          `;
        } else {
          valueContainer.innerHTML = '<div class="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">Значения не найдены</div>';
        }
      } else {
        // Единичный выбор через селект
        let optionsHtml = '';

        if (Array.isArray(availableValues)) {
          optionsHtml = availableValues.map(item => {
            if (typeof item === 'object' && item.id && item.name) {
              return `<option value="${item.id}" data-name="${item.name}" data-description="${item.description || ''}">${item.name}</option>`;
            } else if (typeof item === 'string') {
              return `<option value="${item}">${item}</option>`;
            } else {
              return `<option value="${JSON.stringify(item)}">${JSON.stringify(item)}</option>`;
            }
          }).join('');
        } else if (availableValues && typeof availableValues === 'object') {
          if (availableValues.id && availableValues.name) {
            optionsHtml = `<option value="${availableValues.id}" data-name="${availableValues.name}" data-description="${availableValues.description || ''}">${availableValues.name}</option>`;
          } else {
            optionsHtml = `<option value="${JSON.stringify(availableValues)}">${JSON.stringify(availableValues)}</option>`;
          }
        }

        if (optionsHtml) {
          valueContainer.innerHTML = `
            <select class="variant-attribute-value-select w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">Выберите значение</option>
              ${optionsHtml}
            </select>
          `;
        } else {
          valueContainer.innerHTML = '<div class="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">Значения не найдены</div>';
        }
      }
    } else {
      // Нет доступных значений - простой текстовый ввод
      valueContainer.innerHTML = `
        <input type="text" class="variant-attribute-value-input w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Введите значение">
      `;
    }
  }

    // Добавляем обработчики событий для созданных элементов
    setTimeout(() => {
      const checkRow = document.getElementById(attributeId);
      const checkContainer = checkRow?.querySelector('.variant-attribute-value');

      if (!checkRow) {
        return;
      }

      // Добавляем обработчики для селектов значений
      const valueSelect = checkRow.querySelector('.variant-attribute-value-select');
      const valueInput = checkRow.querySelector('.variant-attribute-value-input');
      const valueCheckboxes = checkRow.querySelectorAll('.variant-attribute-checkbox');

      if (valueSelect) {
        valueSelect.addEventListener('change', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);

          // Автоматическое сворачивание при выборе значения
          if (valueSelect.value) {
            setTimeout(() => {
              collapseAttributeForm(attributeId);
            }, 100);
          }
        });
        valueSelect.addEventListener('input', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);
        });
      }

      if (valueInput) {
        valueInput.addEventListener('input', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);
        });
        valueInput.addEventListener('change', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);
        });
      }

      // Добавляем обработчики для чекбоксов множественного выбора
      valueCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);
        });
        checkbox.addEventListener('input', function(event) {
          event.stopPropagation();
          updateCollapseButtonState(attributeId);
        });
      });

      // Обновляем состояние кнопки после создания элементов
      updateCollapseButtonState(attributeId);
    }, 100);

  } catch (error) {
    // Попытаемся найти valueContainer и показать ошибку
    try {
      const row = document.getElementById(attributeId);
      const valueContainer = row?.querySelector('.variant-attribute-value');
      if (valueContainer) {
        valueContainer.innerHTML = '<p class="text-xs text-red-500 py-1">Ошибка при обработке атрибута</p>';
      }
    } catch (e) {
      // Игнорируем ошибки при показе сообщения об ошибке
    }
  }

  // Добавляем обработчики событий для обновления состояния кнопки сохранения
  setTimeout(() => {
    const row = document.getElementById(attributeId);
    const valueSelect = row?.querySelector('.variant-attribute-value-select');
    const checkboxes = row?.querySelectorAll('.variant-attribute-checkbox');

    if (valueSelect) {
      valueSelect.addEventListener('change', () => {
        updateCollapseButtonState(attributeId);
      });
    }

    if (checkboxes) {
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
          updateCollapseButtonState(attributeId);
        });
      });
    }

    // Обновляем состояние кнопки после создания элементов
    updateCollapseButtonState(attributeId);
  }, 100);
}

/**
 * Проверяет, есть ли хотя бы один вариант товара с указанной ценой
 */
export function hasVariantsWithPricing() {
  const variantRows = document.querySelectorAll('.variant-row');

  for (const row of variantRows) {
    const variantPrice = parseFloat(row.querySelector('.variant-price')?.value || 0);
    const variantCurrency = row.querySelector('.variant-currency')?.value;
    const variantUnit = row.querySelector('.variant-unit')?.value;

    // Проверяем, что у варианта есть корректная цена, валюта и единица измерения
    if (!isNaN(variantPrice) && variantPrice > 0 && variantCurrency && variantUnit) {
      return true;
    }
  }

  return false;
}

/**
 * Проверяет, есть ли хотя бы один вариант с отмеченной основной ценой
 */
export function hasPrimaryPriceVariant() {
  const variantRows = document.querySelectorAll('.variant-row');

  for (const row of variantRows) {
    const isPrimaryPriceCheckbox = row.querySelector('.variant-is-primary-price');

    // Проверяем, что чекбокс существует и отмечен
    if (isPrimaryPriceCheckbox && isPrimaryPriceCheckbox.checked) {
      return true;
    }
  }

  return false;
}

/**
 * Получает главную цену товара согласно приоритетам:
 * 1. Цена из варианта с isPrimaryPrice: true (высший приоритет)
 * 2. Базовая цена товара (средний приоритет)
 * 3. null если нет ни того, ни другого (низший приоритет)
 */
export function getMainPrice(product) {
  // Проверяем есть ли варианты с основной ценой
  if (product.variants && Array.isArray(product.variants)) {
    const primaryVariant = product.variants.find(variant =>
      variant.isPrimaryPrice === true &&
      variant.price &&
      variant.price.value > 0
    );

    if (primaryVariant) {
      return {
        value: primaryVariant.price.value,
        currency: primaryVariant.price.currency,
        unit: primaryVariant.price.unit,
        simvol: primaryVariant.price.simvol || primaryVariant.price.currency,
        source: 'primary_variant'
      };
    }
  }

  // Если нет основного варианта, используем базовую цену
  if (product.basePrice && product.basePrice.value > 0) {
    return {
      value: product.basePrice.value,
      currency: product.basePrice.currency,
      unit: product.basePrice.unit,
      simvol: product.basePrice.simvol || product.basePrice.currency,
      source: 'base_price'
    };
  }

  // Если нет ни того, ни другого
  return null;
}

/**
 * Управляет состоянием полей базовой цены в зависимости от наличия вариантов с ценами и основной цены
 */
export function toggleBasePriceFields() {
  const hasVariants = hasVariantsWithPricing();
  const hasPrimaryPrice = hasPrimaryPriceVariant();

  // Получаем элементы полей базовой цены
  const basePriceInput = document.getElementById('basePrice');
  const currencySelect = document.getElementById('currency');
  const unitSelect = document.getElementById('unit');
  const pricePreview = document.getElementById('price-preview');

  if (!basePriceInput || !currencySelect || !unitSelect) {
    return;
  }

  // Найдем или создадим информационное сообщение
  let infoMessage = document.getElementById('base-price-info-message');

  // Базовая цена должна быть отключена только если есть варианты с ценами И есть основная цена
  const shouldDisableBasePrice = hasVariants && hasPrimaryPrice;

  if (shouldDisableBasePrice) {
    // Если есть варианты с ценами И есть основная цена, отключаем поля базовой цены
    basePriceInput.disabled = true;
    currencySelect.disabled = true;
    unitSelect.disabled = true;

    // Убираем обязательность полей
    basePriceInput.removeAttribute('required');
    currencySelect.removeAttribute('required');
    unitSelect.removeAttribute('required');

    // Добавляем визуальные стили для отключенных полей
    basePriceInput.classList.add('bg-gray-100', 'text-gray-500');
    currencySelect.classList.add('bg-gray-100', 'text-gray-500');
    unitSelect.classList.add('bg-gray-100', 'text-gray-500');

    // Скрываем предварительный просмотр цены
    if (pricePreview) {
      pricePreview.classList.add('hidden');
    }

    // Очищаем значения полей (опционально)
    basePriceInput.value = '';

    // Информационное сообщение теперь отображается через всплывающую подсказку
    // Удаляем синий блок, если он существует
    if (infoMessage) {
      infoMessage.remove();
    }

  } else {
    // Если нет вариантов с ценами ИЛИ есть варианты, но нет основной цены, включаем поля базовой цены
    basePriceInput.disabled = false;
    currencySelect.disabled = false;
    unitSelect.disabled = false;

    // Возвращаем обязательность полей
    basePriceInput.setAttribute('required', '');
    currencySelect.setAttribute('required', '');
    unitSelect.setAttribute('required', '');

    // Убираем визуальные стили отключенных полей
    basePriceInput.classList.remove('bg-gray-100', 'text-gray-500');
    currencySelect.classList.remove('bg-gray-100', 'text-gray-500');
    unitSelect.classList.remove('bg-gray-100', 'text-gray-500');

    // Показываем предварительный просмотр цены
    if (pricePreview) {
      pricePreview.classList.remove('hidden');
    }

    // Удаляем информационное сообщение, если оно существует
    if (infoMessage) {
      infoMessage.remove();
    }
  }
}

/**
 * Сбор данных всех вариантов
 */
export function collectVariantsData() {
  const variants = [];
  const variantRows = document.querySelectorAll('.variant-row');

  variantRows.forEach(row => {
    const variantName = row.querySelector('.variant-name').value.trim();
    const variantPrice = parseFloat(row.querySelector('.variant-price').value);
    const variantCurrency = row.querySelector('.variant-currency').value;
    const variantUnit = row.querySelector('.variant-unit').value;
    const variantSku = row.querySelector('.variant-sku').value.trim();
    const isPrimaryPrice = row.querySelector('.variant-is-primary-price')?.checked || false;

    // Собираем атрибуты варианта
    const variantAttributes = {};
    const attributeRows = row.querySelectorAll('.variant-attribute-row');

    attributeRows.forEach(attrRow => {
      const typeSelect = attrRow.querySelector('.variant-attribute-type');
      const valueSelect = attrRow.querySelector('.variant-attribute-value-select');
      const valueInput = attrRow.querySelector('.variant-attribute-value-input');
      const valueCheckboxes = attrRow.querySelectorAll('.variant-attribute-checkbox:checked');

      if (typeSelect && typeSelect.value) {
        const attributeType = typeSelect.value;
        let attributeValue = null;

        // Проверяем, есть ли выбранные чекбоксы (множественный выбор)
        if (valueCheckboxes.length > 0) {
          const selectedValues = [];
          valueCheckboxes.forEach(checkbox => {
            if (attributeType === 'color_pigments' || attributeType === 'material') {
              // Для цветовых пигментов и материалов собираем объекты
              selectedValues.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                description: checkbox.dataset.description || ''
              });
            } else if (checkbox.dataset.name) {
              // Для других типов с дополнительными данными собираем объекты
              selectedValues.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                description: checkbox.dataset.description || ''
              });
            } else {
              // Для простых типов собираем значения напрямую
              selectedValues.push(checkbox.value);
            }
          });
          attributeValue = selectedValues;
        } else if (valueSelect && valueSelect.value) {
          attributeValue = valueSelect.value;
          // Для размеров парсим JSON
          if (attributeType === 'size') {
            try {
              attributeValue = JSON.parse(valueSelect.value);
            } catch (e) {
              // Игнорируем ошибки парсинга
            }
          } else if (attributeType === 'material') {
            // Для материала находим полный объект по ID
            const selectedAttributes = window.getSelectedProductAttributes();
            const materialAttribute = selectedAttributes[attributeType];
            if (materialAttribute && materialAttribute.value) {
              const materials = Array.isArray(materialAttribute.value) ? materialAttribute.value : [materialAttribute.value];
              const selectedMaterial = materials.find(m => m && m.id === valueSelect.value);
              if (selectedMaterial && selectedMaterial.id) {
                attributeValue = selectedMaterial;
              }
            }
          } else if (attributeType === 'color_pigments') {
            // Для цветовых пигментов находим полный объект по ID из селекта
            const selectedAttributes = window.getSelectedProductAttributes();
            const pigmentAttribute = selectedAttributes[attributeType];
            if (pigmentAttribute && pigmentAttribute.value && valueSelect.value) {
              const pigments = Array.isArray(pigmentAttribute.value) ? pigmentAttribute.value : [pigmentAttribute.value];
              const selectedPigment = pigments.find(p => p && p.id === valueSelect.value);
              if (selectedPigment && selectedPigment.id) {
                attributeValue = selectedPigment;
              }
            }
          }
        } else if (valueInput && valueInput.value.trim()) {
          attributeValue = valueInput.value.trim();
        }

        // Проверяем, что значение валидно (не null, не undefined, не пустая строка)
        if (attributeValue !== null && attributeValue !== undefined &&
            attributeValue !== 'undefined' && attributeValue !== 'null' &&
            (typeof attributeValue !== 'string' || attributeValue.trim() !== '')) {
          variantAttributes[attributeType] = attributeValue;
        } else if (attributeValue !== null) {
          console.warn(`Пропущен невалидный атрибут варианта: ${attributeType} = ${JSON.stringify(attributeValue)}`);
        }
      }
    });

    // Добавляем вариант только если есть название и цена
    if (variantName && !isNaN(variantPrice) && variantPrice > 0 && variantCurrency && variantUnit) {
      // Получаем существующий ID варианта или создаем новый
      let variantId = row.getAttribute('data-variant-id');
      // Вместо генерации id используем sku как id
      if (variantSku) {
        variantId = variantSku;
      } else if (!variantId) {
        variantId = `variant-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      }

      variants.push({
        id: variantId,
        name: variantName,
        attributes: variantAttributes,
        price: {
          value: variantPrice,
          currency: variantCurrency,
          unit: variantUnit
        },
        sku: variantSku || null,
        isPrimaryPrice: isPrimaryPrice
      });
    }
  });

  return variants;
}

/**
 * Сохранение варианта (сворачивание в компактный вид)
 */
export function saveVariant(variantId) {
  const variantRow = document.getElementById(variantId);
  if (!variantRow) return;

  // Собираем данные варианта
  const variantData = collectSingleVariantData(variantRow);

  // Проверяем обязательные поля
  if (!variantData.name.trim()) {
    alert('Пожалуйста, введите название варианта');
    return;
  }

  if (!variantData.price || variantData.price <= 0) {
    alert('Пожалуйста, введите корректную цену');
    return;
  }

  // Обновляем сохраненный вид
  updateSavedVariantView(variantRow, variantData);

  // Переключаем на сохраненный вид
  const editForm = variantRow.querySelector('.variant-edit-form');
  const savedView = variantRow.querySelector('.variant-saved-view');
  const statusBadge = variantRow.querySelector('.variant-status-badge');

  if (editForm && savedView && statusBadge) {
    editForm.classList.add('hidden');
    savedView.classList.remove('hidden');
    statusBadge.textContent = 'Сохранен';
    statusBadge.className = 'variant-status-badge px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800';
  }

  // Обновляем состояние полей базовой цены после сохранения варианта
  toggleBasePriceFields();

  // Уведомляем об изменении
  notifyVariantChange();
}

/**
 * Редактирование варианта (разворачивание формы)
 */
export function editVariant(variantId) {
  const variantRow = document.getElementById(variantId);
  if (!variantRow) return;

  // Переключаем на форму редактирования
  const editForm = variantRow.querySelector('.variant-edit-form');
  const savedView = variantRow.querySelector('.variant-saved-view');
  const statusBadge = variantRow.querySelector('.variant-status-badge');

  if (editForm && savedView && statusBadge) {
    editForm.classList.remove('hidden');
    savedView.classList.add('hidden');
    statusBadge.textContent = 'Редактирование';
    statusBadge.className = 'variant-status-badge px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800';

    // [AI-UNIFY] Применяем space-y-4 для контейнера атрибутов при редактировании
    const attrContainer = editForm.querySelector('.variant-attributes-container');
    if (attrContainer) {
      attrContainer.classList.add('space-y-4');
    }

    // Восстанавливаем значения атрибутов из сохраненных данных
    const variantData = JSON.parse(variantRow.dataset.variantData || '{}');
    if (variantData.attributes && variantData.attributes.length > 0) {
      setTimeout(() => {
        variantData.attributes.forEach(savedAttribute => {
          // Сначала проверяем, есть ли уже строка с таким типом атрибута
          const attributeRows = editForm.querySelectorAll('.variant-attribute-row');
          let foundRow = null;

          attributeRows.forEach(row => {
            const typeSelect = row.querySelector('.variant-attribute-type');
            if (typeSelect && typeSelect.value === savedAttribute.type) {
              foundRow = row;
            }
          });

          // Если строка не найдена, добавляем новую
          if (!foundRow) {
            const addButton = editForm.querySelector('.add-variant-attribute-btn');
            if (addButton) {
              addButton.click();
              // Получаем последнюю добавленную строку
              const newRows = editForm.querySelectorAll('.variant-attribute-row');
              foundRow = newRows[newRows.length - 1];

              // Устанавливаем тип атрибута
              const typeSelect = foundRow?.querySelector('.variant-attribute-type');
              if (typeSelect) {
                typeSelect.value = savedAttribute.type;
                // Триггерим событие change для генерации интерфейса значений
                typeSelect.dispatchEvent(new Event('change'));
              }
            }
          }

          // Устанавливаем значение атрибута
          if (foundRow) {
            // Увеличиваем задержку и добавляем проверку на загрузку опций
            const waitForOptions = () => {
              const valueSelect = foundRow.querySelector('.variant-attribute-value-select');
              const valueInput = foundRow.querySelector('.variant-attribute-value-input');

              if (valueSelect && savedAttribute.value) {
                // Проверяем, что опции загружены (больше чем просто placeholder)
                if (valueSelect.options.length <= 1) {
                  setTimeout(waitForOptions, 200);
                  return;
                }
                let valueToSet = null;

                // Проверяем разные форматы сохраненных данных
                if (typeof savedAttribute.value === 'object' && savedAttribute.value !== null && savedAttribute.value.id) {
                  valueToSet = savedAttribute.value.id;
                } else if (typeof savedAttribute.value === 'string') {
                  valueToSet = savedAttribute.value;
                } else {
                  valueToSet = savedAttribute.value;
                }

                // Добавляем задержку для полной загрузки опций
                setTimeout(() => {
                  // Проверяем, что опция существует в select
                  let option = valueSelect.querySelector(`option[value="${valueToSet}"]`);
                  if (option) {
                    valueSelect.value = valueToSet;
                    // Принудительно обновляем отображение
                    valueSelect.dispatchEvent(new Event('change'));

                  } else {
                    // Попробуем найти по имени
                    let searchName = null;
                    if (savedAttribute.value && typeof savedAttribute.value === 'object' && savedAttribute.value.name) {
                      searchName = savedAttribute.value.name;
                    } else if (typeof savedAttribute.value === 'string') {
                      searchName = savedAttribute.value;
                    }

                    if (searchName) {
                      const optionByName = Array.from(valueSelect.options).find(opt =>
                        opt.textContent.trim() === searchName.trim()
                      );
                      if (optionByName) {
                        valueSelect.value = optionByName.value;
                        // Принудительно обновляем отображение
                        valueSelect.dispatchEvent(new Event('change'));

                      } else {

                      }
                    }
                  }
                }, 200); // Увеличиваем задержку до 200мс
              } else if (valueInput && savedAttribute.value) {
                valueInput.value = savedAttribute.value;
              }
            };

            // Запускаем ожидание загрузки опций
            waitForOptions();
          }
        });
      }, 100);
    }
  }

  // Обновляем состояние полей базовой цены при редактировании варианта
  toggleBasePriceFields();
}

/**
 * Сбор данных одного варианта
 */
function collectSingleVariantData(variantRow) {
  const nameInput = variantRow.querySelector('.variant-name');
  const priceInput = variantRow.querySelector('.variant-price');
  const currencySelect = variantRow.querySelector('.variant-currency');
  const unitSelect = variantRow.querySelector('.variant-unit');
  const skuInput = variantRow.querySelector('.variant-sku');
  const isPrimaryPriceCheckbox = variantRow.querySelector('.variant-is-primary-price');

  // Собираем атрибуты
  const attributes = [];
  const attributeRows = variantRow.querySelectorAll('.variant-attribute-row');

  attributeRows.forEach(row => {
    const typeSelect = row.querySelector('.variant-attribute-type');
    const valueSelect = row.querySelector('.variant-attribute-value-select');
    const valueInput = row.querySelector('.variant-attribute-value-input');

    if (typeSelect && typeSelect.value) {
      const attributeData = {
        type: typeSelect.value,
        value: null
      };

      if (valueSelect && valueSelect.value) {
        // Для color_pigments сохраняем полный объект
        if (typeSelect.value === 'color_pigments') {
          // Получаем текст выбранной опции
          const selectedOption = valueSelect.options[valueSelect.selectedIndex];
          const selectedText = selectedOption ? selectedOption.text : '';

          // Сохраняем как объект с id и name
          attributeData.value = {
            id: valueSelect.value,
            name: selectedText
          };
        } else {
          attributeData.value = valueSelect.value;
        }
      } else if (valueInput && valueInput.value) {
        attributeData.value = valueInput.value;
      }

      if (attributeData.value) {
        attributes.push(attributeData);
      }
    }
  });

  return {
    name: nameInput?.value || '',
    price: {
      value: parseFloat(priceInput?.value) || 0,
      currency: currencySelect?.value || 'BYN',
      unit: unitSelect?.value || ''
    },
    sku: skuInput?.value || '',
    isPrimaryPrice: isPrimaryPriceCheckbox?.checked || false,
    attributes: attributes
  };
}

/**
 * Обновление сохраненного вида варианта
 */
function updateSavedVariantView(variantRow, variantData) {
  const savedNameEl = variantRow.querySelector('.variant-saved-name');
  const savedPriceEl = variantRow.querySelector('.variant-saved-price');
  const savedCurrencyEl = variantRow.querySelector('.variant-saved-currency');
  const savedAttributesEl = variantRow.querySelector('.variant-saved-attributes');
  const primaryPriceBadge = variantRow.querySelector('.variant-primary-price-badge');

  if (savedNameEl) {
    savedNameEl.textContent = variantData.name;
  }

  if (savedPriceEl) {
    savedPriceEl.textContent = variantData.price.value.toFixed(2);
  }

  if (savedCurrencyEl) {
    savedCurrencyEl.textContent = variantData.price.currency;
  }

  if (savedAttributesEl) {
    if (variantData.attributes.length > 0) {
      const attributeTexts = variantData.attributes.map(attr => {
        if (typeof attr.value === 'string') {
          try {
            const parsed = JSON.parse(attr.value);
            // Для размеров
            if (parsed.length && parsed.width && parsed.height) {
              return `${parsed.length}×${parsed.width}×${parsed.height} мм`;
            }
            // Для других объектов с name
            if (parsed.name) {
              return parsed.name;
            }
            return attr.value;
          } catch {
            // Если не JSON, то это простая строка (например, цвет или текстура)
            return attr.value;
          }
        }
        return attr.value;
      });
      savedAttributesEl.textContent = `• ${attributeTexts.join(', ')}`;
    } else {
      savedAttributesEl.textContent = '';
    }
  }

  // Показываем/скрываем бейдж "Основная цена"
  if (primaryPriceBadge) {
    if (variantData.isPrimaryPrice) {
      primaryPriceBadge.classList.remove('hidden');
    } else {
      primaryPriceBadge.classList.add('hidden');
    }
  }

  // Сохраняем данные варианта для последующего восстановления
  variantRow.dataset.variantData = JSON.stringify(variantData);
}

// Экспортируем функции в глобальную область для обратной совместимости
if (typeof window !== 'undefined') {
  window.initializeVariantsComponent = initializeVariantsComponent;
  window.addVariantRow = addVariantRow;
  window.removeVariantRow = removeVariantRow;
  window.collectVariantsData = collectVariantsData;
  window.handleVariantAttributeTypeChange = handleVariantAttributeTypeChange;
  window.addVariantAttributeRow = addVariantAttributeRow;
  window.removeVariantAttributeRow = removeVariantAttributeRow;
  window.saveVariant = saveVariant;
  window.editVariant = editVariant;
  window.updateCollapseButtonState = updateCollapseButtonState;
  window.collapseAttributeForm = collapseAttributeForm;
  window.expandAttributeForm = expandAttributeForm;
  window.generateVariantSKU = generateVariantSKU;
  window.hasVariantsWithPricing = hasVariantsWithPricing;
  window.hasPrimaryPriceVariant = hasPrimaryPriceVariant;
  window.toggleBasePriceFields = toggleBasePriceFields;
  window.getMainPrice = getMainPrice;
}
