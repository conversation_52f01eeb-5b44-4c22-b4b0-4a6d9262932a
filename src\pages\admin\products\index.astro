---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import Card from '../../../components/ui/Card.astro';
import CardHeader from '../../../components/ui/CardHeader.astro';
import CardTitle from '../../../components/ui/CardTitle.astro';
import CardDescription from '../../../components/ui/CardDescription.astro';
import CardContent from '../../../components/ui/CardContent.astro';
import Button from '../../../components/ui/Button.astro';
import Badge from '../../../components/ui/Badge.astro';
import ProductStatusBadge from '../../../components/admin/ProductStatusBadge.astro';
import Input from '../../../components/ui/Input.astro';
import Select from '../../../components/ui/Select.astro';
import Label from '../../../components/ui/Label.astro';
import formatUnit from '../../../utils/formatUnit.js';
import formatPrice from '../../../utils/formatPrice.js';


// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о товарах и категориях
import productsData from '../../../../data/product/products.json';
import categoriesFile from '../../../../data/product/categories.json';
import settingsProduct from '../../../../data/product/settings-product.json';
// Фильтруем только активные категории (activeForProducts = true)
const categoriesData = categoriesFile.categories.filter(cat => cat.activeForProducts !== false);

// Статистика
const totalProducts = productsData.length;
const publishedProducts = productsData.filter(p => (p.status === 'published') || (!p.status && p.inStock)).length;
const draftProducts = productsData.filter(p => p.status === 'draft').length;
const unpublishedProducts = productsData.filter(p => (p.status === 'unpublished') || (!p.status && !p.inStock)).length;
---

<AdminLayout title="Управление товарами | LuxBeton">
  <div class="space-y-8">
    <!-- Заголовок и действия -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900">Управление товарами</h1>
        <p class="mt-2 text-sm text-gray-600">Управляйте каталогом товаров, редактируйте и добавляйте новые позиции</p>
      </div>
      <div class="mt-4 sm:mt-0 flex gap-3">
        <a
          href="/admin/products/settings-product"
          class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          title="Настройки товаров"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Настройки товаров
        </a>
        <a
          href="/admin/products/new"
          class="add-product-btn inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
          style="background-color: #3b82f6;"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
          Добавить товар
        </a>
      </div>
    </div>

    <!-- Фильтры и поиск на сером фоне -->
    <div class="w-full bg-gray-50 rounded-md py-4 mb-6">
      <div class="flex flex-col md:flex-row md:items-end md:space-x-3 space-y-3 md:space-y-0">
        <!-- Поиск -->
        <div class="flex-1 min-w-[180px]">
          <Label for="search-input" class="text-gray-700 sr-only">Поиск товара</Label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-4 w-4 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
            </div>
            <Input
              id="search-input"
              type="search"
              placeholder="Поиск по ID или названию..."
              class="pl-9 h-9 rounded-md border border-input bg-background text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-colors"
              aria-label="Поиск товара"
            />
          </div>
        </div>
        <!-- Категория -->
        <div class="min-w-[160px]">
          <Label for="category-filter" class="text-gray-700 sr-only">Категория</Label>
          <Select id="category-filter" class="h-9 rounded-md border border-input bg-background text-sm">
            <option value="">Все категории</option>
            {categoriesData.map(category => (
              <option value={category.name}>{category.name}</option>
            ))}
          </Select>
        </div>
        <!-- Статус -->
        <div class="min-w-[160px]">
          <Label for="status-filter" class="text-gray-700 sr-only">Статус публикации</Label>
          <Select id="status-filter" class="h-9 rounded-md border border-input bg-background text-sm">
            <option value="">Все статусы</option>
            <option value="published">Опубликован</option>
            <option value="draft">Черновик</option>
            <option value="unpublished">Не опубликован</option>
          </Select>
        </div>
        <!-- Кнопки действий -->
        <div class="flex gap-2 md:ml-2">
          <button
            id="apply-filters"
            type="button"
            class="h-9 px-4 border border-blue-300 text-blue-600 bg-blue-50 hover:bg-blue-100 font-medium rounded-md text-sm transition-colors"
          >
            Применить
          </button>
          <button
            id="reset-filters"
            type="button"
            class="h-9 px-4 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 font-medium rounded-md text-sm transition-colors"
          >
            Сбросить
          </button>
        </div>
      </div>
    </div>

    <!-- Таблица товаров -->
    <Card class="animate-slide-in" style="animation-delay: 0.3s;">
      <CardHeader>
        <div class="flex items-center justify-between">
          <div>
            <CardTitle class="flex items-center gap-2">
              <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
              </svg>
              Список товаров
            </CardTitle>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-500">
              Показано: <span id="results-count" class="font-medium text-gray-900">{totalProducts}</span> из {totalProducts}
            </div>
            <div class="text-xs text-gray-400 mt-1 space-x-4">
              <span>Опубликовано: <span class="text-green-600 font-medium">{publishedProducts}</span></span>
              <span>Черновики: <span class="text-gray-600 font-medium">{draftProducts}</span></span>
              <span>Не опубликовано: <span class="text-red-600 font-medium">{unpublishedProducts}</span></span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent class="p-0">
        <!-- Desktop Table -->
        <div class="hidden lg:block overflow-x-auto custom-scrollbar">
          <table id="products-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Изображение</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Название</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Категория</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Цена</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
              </tr>
            </thead>
            <tbody id="products-tbody" class="bg-white divide-y divide-gray-200">
              {productsData.map(product => (
                <tr data-category={product.category} data-price={product.basePrice?.value || (product.variants?.find(v => v.price?.value)?.price?.value || 0)} data-status={product.status || (product.inStock ? 'published' : 'unpublished')} class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">{product.id}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-12 w-12 flex-shrink-0">
                      <img src={`/product/${product.images.main}`} alt={product.name} class="h-12 w-12 object-cover rounded-lg shadow-sm" />
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{product.name}</div>
                    <div class="text-sm text-gray-500 truncate max-w-xs">{product.shortDescription}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge variant="outline">{product.category}</Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {(() => {
                      // Если есть базовая цена, показываем её
                      if (product.basePrice && product.basePrice.value) {
                        const currencyConfig = settingsProduct.currencies.supported.find(
                          curr => curr.key === product.basePrice.currency
                        );
                        const simvol = currencyConfig?.simvol || product.basePrice.currency || '₽';

                        return formatPrice({
                          amount: product.basePrice.value,
                          simvol: simvol,
                          format: '{amount} {simvol}',
                          decimalSeparator: '.',
                          thousandsSeparator: ' ',
                          decimals: 2
                        }) + '/' + formatUnit(product.basePrice.unit, settingsProduct as any);
                      }

                      // Если нет базовой цены, но есть варианты с ценами
                      if (product.variants && product.variants.length > 0) {
                        const variantWithPrice = product.variants.find(v => v.price && v.price.value);
                        if (variantWithPrice) {
                          const currencyConfig = settingsProduct.currencies.supported.find(
                            curr => curr.key === variantWithPrice.price.currency
                          );
                          const simvol = currencyConfig?.simvol || variantWithPrice.price.currency || '₽';

                          return 'от ' + formatPrice({
                            amount: variantWithPrice.price.value,
                            simvol: simvol,
                            format: '{amount} {simvol}',
                            decimalSeparator: '.',
                            thousandsSeparator: ' ',
                            decimals: 2
                          }) + '/' + formatUnit(variantWithPrice.price.unit, settingsProduct as any);
                        }
                      }

                      return 'Цена не указана';
                    })()}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <ProductStatusBadge
                      status={product.status || (product.inStock ? 'published' : 'unpublished')}
                      size="sm"
                    />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <a href={`/admin/products/edit/${product.id}`} class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
                        Редактировать
                      </a>
                      <button
                        data-id={product.id}
                        class="delete-product inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                      >
                        Удалить
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <!-- Mobile Cards -->
        <div class="lg:hidden space-y-4 p-6" id="products-mobile">
          {productsData.map(product => (
            <div data-category={product.category} data-price={product.basePrice?.value || 0} data-status={product.status || (product.inStock ? 'published' : 'unpublished')} class="product-mobile-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
              <div class="flex items-start space-x-4">
                <div class="h-16 w-16 flex-shrink-0">
                  <img src={`/product/${product.images.main}`} alt={product.name} class="h-16 w-16 object-cover rounded-lg" />
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-start justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 truncate">{product.name}</h3>
                      <p class="text-xs text-gray-500 font-mono">{product.id}</p>
                    </div>
                    <div class="ml-2">
                      <ProductStatusBadge
                        status={product.status || (product.inStock ? 'published' : 'unpublished')}
                        size="sm"
                      />
                    </div>
                  </div>
                  <div class="mt-2 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <Badge variant="outline" class="text-xs">{product.category}</Badge>
                      <span class="text-sm font-medium text-gray-900">
                        {(() => {
                          // Если есть базовая цена, показываем её
                          if (product.basePrice && product.basePrice.value) {
                            const currencyConfig = settingsProduct.currencies.supported.find(
                              curr => curr.key === product.basePrice.currency
                            );
                            const simvol = currencyConfig?.simvol || product.basePrice.currency || '₽';

                            return formatPrice({
                              amount: product.basePrice.value,
                              simvol: simvol,
                              format: '{amount} {simvol}',
                              decimalSeparator: '.',
                              thousandsSeparator: ' ',
                              decimals: 2
                            }) + '/' + formatUnit(product.basePrice.unit, settingsProduct as any);
                          }

                          // Если нет базовой цены, но есть варианты с ценами
                          if (product.variants && product.variants.length > 0) {
                            const variantWithPrice = product.variants.find(v => v.price && v.price.value);
                            if (variantWithPrice) {
                              const currencyConfig = settingsProduct.currencies.supported.find(
                                curr => curr.key === variantWithPrice.price.currency
                              );
                              const simvol = currencyConfig?.simvol || variantWithPrice.price.currency || '₽';

                              return 'от ' + formatPrice({
                                amount: variantWithPrice.price.value,
                                simvol: simvol,
                                format: '{amount} {simvol}',
                                decimalSeparator: '.',
                                thousandsSeparator: ' ',
                                decimals: 2
                              }) + '/' + formatUnit(variantWithPrice.price.unit, settingsProduct as any);
                            }
                          }

                          return 'Цена не указана';
                        })()}
                      </span>
                    </div>
                  </div>
                  <div class="mt-3 flex space-x-2">
                    <a href={`/admin/products/edit/${product.id}`} class="flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
                      Редактировать
                    </a>
                    <button
                      data-id={product.id}
                      class="delete-product flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                    >
                      Удалить
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки добавления товара */
  .add-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Анимации */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.6s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Улучшенные стили для таблицы */
  #products-table tbody tr:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
  }

  /* Стили для мобильных карточек */
  .product-mobile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
  }

  /* Стили для поиска */
  #search-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Кастомный скроллбар */
  .custom-scrollbar::-webkit-scrollbar {
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Стили для бейджей */
  .badge-success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .badge-destructive {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
</style>

<script>
  // Сохраняем исходные данные для фильтрации
  const allRows = Array.from(document.querySelectorAll('#products-tbody tr'));
  const allMobileCards = Array.from(document.querySelectorAll('.product-mobile-card'));

  // Функция фильтрации и сортировки
  function filterAndSortProducts() {
    const searchInput = (document.getElementById('search-input') as HTMLInputElement)?.value.toLowerCase() || '';
    const categoryFilter = (document.getElementById('category-filter') as HTMLSelectElement)?.value || '';
    const statusFilter = (document.getElementById('status-filter') as HTMLSelectElement)?.value || '';

    // Фильтрация для desktop таблицы
    let filteredRows = allRows.filter(row => {
      const category = (row as HTMLElement).dataset.category;
      const status = (row as HTMLElement).dataset.status;

      // Получаем ID и название товара для поиска
      const productId = row.querySelector('td:first-child')?.textContent?.toLowerCase() || '';
      const productName = row.querySelector('td:nth-child(3) .text-sm.font-medium')?.textContent?.toLowerCase() || '';

      const searchMatch = !searchInput ||
        productId.includes(searchInput) ||
        productName.includes(searchInput);
      const categoryMatch = !categoryFilter || category === categoryFilter;
      const statusMatch = !statusFilter || status === statusFilter;

      return searchMatch && categoryMatch && statusMatch;
    });

    // Фильтрация для мобильных карточек
    let filteredCards = allMobileCards.filter(card => {
      const category = (card as HTMLElement).dataset.category;
      const status = (card as HTMLElement).dataset.status;

      // Получаем ID и название товара для поиска
      const productId = card.querySelector('.text-xs.text-gray-500.font-mono')?.textContent?.toLowerCase() || '';
      const productName = card.querySelector('.text-sm.font-medium.text-gray-900')?.textContent?.toLowerCase() || '';

      const searchMatch = !searchInput ||
        productId.includes(searchInput) ||
        productName.includes(searchInput);
      const categoryMatch = !categoryFilter || category === categoryFilter;
      const statusMatch = !statusFilter || status === statusFilter;

      return searchMatch && categoryMatch && statusMatch;
    });

    // Обновление таблицы
    const tbody = document.getElementById('products-tbody');
    if (tbody) {
      tbody.innerHTML = '';
      filteredRows.forEach(row => tbody.appendChild(row));
    }

    // Обновление мобильных карточек
    const mobileContainer = document.getElementById('products-mobile');
    if (mobileContainer) {
      mobileContainer.innerHTML = '';
      filteredCards.forEach(card => mobileContainer.appendChild(card));
    }

    // Обновление счетчика результатов
    const resultsCount = document.getElementById('results-count');
    if (resultsCount) {
      resultsCount.textContent = filteredRows.length.toString();
    }

    // Переподключение обработчиков удаления
    attachDeleteHandlers();
  }

  // Функция для подключения обработчиков удаления
  function attachDeleteHandlers() {
    document.querySelectorAll('.delete-product').forEach(button => {
      button.removeEventListener('click', handleDelete); // Удаляем старые обработчики
      button.addEventListener('click', handleDelete);
    });
  }

  // Обработчик удаления товара
  async function handleDelete(event) {
    const id = event.target.getAttribute('data-id');

    // Найдем название товара для более информативного сообщения
    const productCard = event.target.closest('.product-card');
    const productName = productCard?.querySelector('h3')?.textContent?.trim() || 'этот товар';

    const confirmed = await window.confirmModal?.show({
      title: 'Подтверждение удаления',
      message: `Вы уверены, что хотите удалить товар "${productName}"? Это действие нельзя отменить.`,
      confirmText: 'Удалить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
    });

    if (confirmed) {
      try {
        const response = await fetch(`/api/admin/products?id=${id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          window.location.reload();
        } else {
          await window.confirmModal?.show({
            title: 'Ошибка',
            message: 'Не удалось удалить товар. Попробуйте еще раз.',
            confirmText: 'ОК',
            cancelText: '',
            confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
          });
        }
      } catch (error) {
        console.error('Ошибка:', error);
        await window.confirmModal?.show({
          title: 'Ошибка',
          message: 'Произошла ошибка при удалении товара. Проверьте подключение к интернету и попробуйте еще раз.',
          confirmText: 'ОК',
          cancelText: '',
          confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
        });
      }
    }
  }

  // Обработчики событий для фильтров
  document.getElementById('apply-filters')?.addEventListener('click', filterAndSortProducts);

  document.getElementById('reset-filters')?.addEventListener('click', () => {
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const categoryFilter = document.getElementById('category-filter') as HTMLSelectElement;
    const statusFilter = document.getElementById('status-filter') as HTMLSelectElement;

    if (searchInput) searchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (statusFilter) statusFilter.value = '';

    // Восстанавливаем исходный порядок
    const tbody = document.getElementById('products-tbody');
    const mobileContainer = document.getElementById('products-mobile');

    if (tbody) {
      tbody.innerHTML = '';
      allRows.forEach(row => tbody.appendChild(row));
    }

    if (mobileContainer) {
      mobileContainer.innerHTML = '';
      allMobileCards.forEach(card => mobileContainer.appendChild(card));
    }

    // Сброс счетчика результатов
    const resultsCount = document.getElementById('results-count');
    if (resultsCount) {
      resultsCount.textContent = allRows.length.toString();
    }

    attachDeleteHandlers();
  });

  // Автоматическое применение фильтров при изменении
  document.getElementById('search-input')?.addEventListener('input', filterAndSortProducts);
  document.getElementById('category-filter')?.addEventListener('change', filterAndSortProducts);
  document.getElementById('status-filter')?.addEventListener('change', filterAndSortProducts);

  // Инициализация обработчиков удаления
  attachDeleteHandlers();
</script>
