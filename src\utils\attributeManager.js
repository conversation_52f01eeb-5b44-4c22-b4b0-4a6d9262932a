/**
 * Утилиты для управления атрибутами товаров
 */

// Счетчик для генерации уникальных ID атрибутов
let attributeCounter = 0;

/**
 * Инициализация компонента атрибутов
 */
export function initializeAttributesComponent() {
  const addAttributeBtn = document.getElementById('add-attribute-btn');
  if (addAttributeBtn) {
    addAttributeBtn.addEventListener('click', addAttributeRow);
  }
}

/**
 * Получение выбранных атрибутов товара
 */
export function getSelectedProductAttributes() {
  try {
    const attributes = {};
    const attributeRows = document.querySelectorAll('.attribute-row');


  attributeRows.forEach((row) => {
    const typeSelect = row.querySelector('.attribute-type-select');
    const attributeType = typeSelect?.value;

    if (!attributeType) return;

    let attributeValue = null;
    
    // Если атрибут свернут, берем значение из data-атрибута
    if (row.classList.contains('collapsed')) {
      const savedValue = row.getAttribute('data-current-value');
      if (savedValue) {
        try {
          attributeValue = JSON.parse(savedValue);
        } catch (e) {
          attributeValue = savedValue;
        }
      }
    } else {
      // Если атрибут развернут, собираем данные из формы
      attributeValue = getCurrentAttributeValue(row, attributeType);
    }

    if (attributeValue !== null && attributeValue !== undefined && attributeValue !== "" && attributeValue !== false) {
      attributes[attributeType] = {
        type: attributeType,
        name: window.attributeTypes[attributeType]?.name || attributeType,
        value: attributeValue,
        data: window.attributesData[attributeType]
      };
    } else {
    }
  });


  return attributes;

  } catch (error) {
    return {};
  }
}

/**
 * Получение текущего значения атрибута из формы
 */
export function getCurrentAttributeValue(row, attributeType) {
  if (attributeType === 'colors') {
    const checkboxes = row.querySelectorAll('.attribute-checkbox:checked');
    return Array.from(checkboxes).map(cb => ({
      id: cb.dataset.id,
      name: cb.dataset.name,
      hex: cb.dataset.hex
    }));
  } else if (attributeType === 'size') {
    const sizes = [];

    // Собираем выбранные предустановленные размеры
    const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
    selectedCheckboxes.forEach(checkbox => {
      try {
        const size = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
        sizes.push(size);
      } catch (error) {
        // Игнорируем ошибки парсинга
      }
    });

    // Собираем пользовательские размеры
    const customSizes = row.querySelectorAll('.custom-size-item');
    customSizes.forEach(item => {
      try {
        const size = JSON.parse(item.getAttribute('data-size'));
        sizes.push(size);
      } catch (error) {
        // Игнорируем ошибки парсинга
      }
    });

    return sizes.length > 0 ? sizes : null;
  } else if (attributeType === 'color_pigments') {
    // Цветовые пигменты - множественный выбор с чекбоксами
    const pigments = [];
    const checkedPigments = row.querySelectorAll('.attribute-checkbox:checked');
    checkedPigments.forEach(checkbox => {
      try {
        const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
        pigments.push(pigment);
      } catch (error) {
        // Игнорируем ошибки парсинга
      }
    });
    return pigments.length > 0 ? pigments : null;
  } else if (['surfaces', 'patterns'].includes(attributeType)) {
    const select = row.querySelector('.attribute-value-select');
    const selectedOption = select?.selectedOptions[0];
    if (selectedOption && selectedOption.value) {
      return {
        id: selectedOption.value,
        name: selectedOption.dataset.name,
        description: selectedOption.dataset.description
      };
    }
  } else {
    const select = row.querySelector('.attribute-value-select');
    if (select && select.value) {
      return select.value;
    }
  }
  return null;
}

/**
 * Сбор данных всех атрибутов
 */
export function collectAttributesData() {
  const attributes = {};
  const attributeRows = document.querySelectorAll('.attribute-row');

  attributeRows.forEach((row) => {
    const typeSelect = row.querySelector('.attribute-type-select');
    const attributeType = typeSelect?.value;

    if (!attributeType) {
      return;
    }

    let attributeValue = null;

    // Если атрибут свернут, берем значение из data-атрибута
    if (row.classList.contains('collapsed')) {
      const savedValue = row.getAttribute('data-current-value');
      if (savedValue) {
        try {
          attributeValue = JSON.parse(savedValue);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      }
    } else {
      // Если атрибут развернут, собираем данные из формы
      attributeValue = getCurrentAttributeValue(row, attributeType);
    }

    if (attributeValue !== null && attributeValue !== undefined && attributeValue !== "") {
      attributes[attributeType] = {
        type: attributeType,
        name: window.attributeTypes[attributeType]?.name || attributeType,
        value: attributeValue,
        data: window.attributesData[attributeType]
      };
    }
  });

  return attributes;
}

/**
 * Добавление нового атрибута
 */
export function addAttributeRow() {
  const container = document.getElementById('attributes-container');
  if (!container) return;

  // Сворачиваем все существующие атрибуты перед добавлением нового
  collapseAllAttributes();

  const attributeId = `attribute-${++attributeCounter}`;
  const attributeRow = document.createElement('div');
  attributeRow.className = 'attribute-row bg-gray-50 p-4 rounded-lg border';
  attributeRow.id = attributeId;

  attributeRow.innerHTML = `
    <div class="attribute-header grid grid-cols-1 md:grid-cols-12 gap-4">
      <div class="md:col-span-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Тип атрибута</label>
        <select class="attribute-type-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" data-attribute-id="${attributeId}">
          <option value="">Выберите тип атрибута</option>
          ${Object.entries(window.attributeTypes).map(([key, config]) =>
            `<option value="${key}">${config.name}</option>`
          ).join('')}
        </select>
      </div>
      <div class="attribute-value-container md:col-span-6">
        <label class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
        <div class="attribute-value-content">
          <p class="text-sm text-gray-500">Сначала выберите тип атрибута</p>
        </div>
      </div>
      <div class="md:col-span-2 flex items-end">
        <button type="button" class="remove-attribute-btn w-full px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 border border-red-300 rounded text-sm" data-attribute-id="${attributeId}">
          ×
        </button>
      </div>
    </div>
  `;

  container.appendChild(attributeRow);

  // Добавляем обработчики событий
  const typeSelect = attributeRow.querySelector('.attribute-type-select');
  const removeBtn = attributeRow.querySelector('.remove-attribute-btn');

  if (typeSelect) {
    typeSelect.addEventListener('change', function() {
      handleAttributeTypeChange(this);
    });

    // Добавляем обработчик для сворачивания других атрибутов при фокусе
    typeSelect.addEventListener('focus', function() {
      collapseOtherAttributes(attributeId);
    });
  }

  if (removeBtn) {
    removeBtn.addEventListener('click', function() {
      removeAttributeRow(this.dataset.attributeId);
    });
  }

  // Обновляем доступные атрибуты в вариантах
  updateVariantAttributeOptions();
}

/**
 * Удаление атрибута
 */
export function removeAttributeRow(attributeId) {
  const row = document.getElementById(attributeId);
  if (row) {
    row.remove();
    // Обновляем доступные атрибуты в вариантах после удаления
    updateVariantAttributeOptions();
  }
}

/**
 * Сворачивание всех атрибутов
 */
export function collapseAllAttributes() {
  const attributeRows = document.querySelectorAll('.attribute-row');
  attributeRows.forEach(row => {
    const typeSelect = row.querySelector('.attribute-type-select');
    if (typeSelect && typeSelect.value) {
      collapseAttributeRow(row, typeSelect.value);
    }
  });
}

/**
 * Сворачивание других атрибутов кроме указанного
 */
export function collapseOtherAttributes(excludeAttributeId) {
  const attributeRows = document.querySelectorAll('.attribute-row');
  attributeRows.forEach(row => {
    if (row.id !== excludeAttributeId) {
      const typeSelect = row.querySelector('.attribute-type-select');
      if (typeSelect && typeSelect.value) {
        collapseAttributeRow(row, typeSelect.value);
      }
    }
  });
}

/**
 * Сворачивание конкретного атрибута
 */
export function collapseAttributeRow(row, attributeType) {
  const currentValue = getCurrentAttributeValue(row, attributeType);
  if (currentValue === null) return;

  // Сохраняем текущее значение в data-атрибуте
  row.setAttribute('data-current-value', JSON.stringify(currentValue));
  row.classList.add('collapsed');

  // Создаем компактное отображение
  const valueContainer = row.querySelector('.attribute-value-content');
  if (valueContainer) {
    const displayValue = getDisplayValue(currentValue, attributeType);
    valueContainer.innerHTML = `
      <div class="flex items-center justify-between bg-white p-2 rounded border">
        <span class="text-sm text-gray-700">${displayValue}</span>
        <button type="button" class="text-blue-600 hover:text-blue-800 text-xs" onclick="expandAttributeRow('${row.id}')">
          Изменить
        </button>
      </div>
    `;
  }
}

/**
 * Получение отображаемого значения для свернутого атрибута
 */
export function getDisplayValue(value, attributeType) {
  if (Array.isArray(value)) {
    if (value.length === 0) return 'Не выбрано';
    if (value.length === 1) {
      return value[0].name || value[0];
    }
    return `${value[0].name || value[0]} и еще ${value.length - 1}`;
  } else if (typeof value === 'object' && value !== null) {
    return value.name || value.id || JSON.stringify(value);
  }
  return value || 'Не выбрано';
}

/**
 * Обновление доступных атрибутов в вариантах
 */
export function updateVariantAttributeOptions() {
  const selectedAttributes = getSelectedProductAttributes();
  const variantAttributeSelects = document.querySelectorAll('.variant-attribute-type');

  variantAttributeSelects.forEach(select => {
    const currentValue = select.value;
    
    // Очищаем и заполняем заново
    select.innerHTML = '<option value="">Выберите атрибут</option>';
    
    Object.entries(selectedAttributes).forEach(([key, attr]) => {
      const option = document.createElement('option');
      option.value = key;
      option.textContent = attr.name;
      if (key === currentValue) {
        option.selected = true;
      }
      select.appendChild(option);
    });

    // Если текущее значение больше не доступно, сбрасываем
    if (currentValue && !selectedAttributes[currentValue]) {
      select.value = '';
      const row = select.closest('.variant-attribute-row');
      if (row) {
        const valueContainer = row.querySelector('.variant-attribute-value');
        if (valueContainer) {
          valueContainer.innerHTML = '<p class="text-xs text-gray-500 py-1">Выберите тип атрибута</p>';
        }
      }
    }
  });
}

// Экспортируем функции в глобальную область для обратной совместимости
if (typeof window !== 'undefined') {
  window.initializeAttributesComponent = initializeAttributesComponent;
  window.getSelectedProductAttributes = getSelectedProductAttributes;
  window.getCurrentAttributeValue = getCurrentAttributeValue;
  window.collectAttributesData = collectAttributesData;
  window.addAttributeRow = addAttributeRow;
  window.removeAttributeRow = removeAttributeRow;
  window.collapseAllAttributes = collapseAllAttributes;
  window.collapseOtherAttributes = collapseOtherAttributes;
  window.collapseAttributeRow = collapseAttributeRow;
  window.getDisplayValue = getDisplayValue;
  window.updateVariantAttributeOptions = updateVariantAttributeOptions;
}
