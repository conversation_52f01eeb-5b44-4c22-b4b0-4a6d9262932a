---
import ConfirmModal from '../components/ui/ConfirmModal.astro';
import ToastNotification from '../components/ui/ToastNotification.astro';

export interface Props {
  title: string;
}

const { title } = Astro.props;
const currentPath = Astro.url.pathname;
---

<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{title}</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/css/grid-container.css">

  <!-- EditorJS основные скрипты -->
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/header@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/paragraph@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/quote@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/code@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/delimiter@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/table@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/link@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/image@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/editorjs-button@latest"></script>

  <!-- Grid Container скрипты - загружаем в head для доступности -->
  <script src="/js/editorjs-tools/grid-container.js"></script>
  <script src="/js/grid-container-manager.js"></script>
  <style>
    /* Дополнительные стили для админ-панели */
    .admin-sidebar {
      min-height: calc(100vh - 4rem);
    }

    /* Анимации */
    .sidebar-transition {
      transition: transform 0.3s ease-in-out;
    }

    /* Мобильное меню */
    @media (max-width: 768px) {
      .mobile-sidebar {
        transform: translateX(-100%);
      }
      .mobile-sidebar.open {
        transform: translateX(0);
      }
    }

    /* Улучшенные анимации */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideIn {
      from { transform: translateX(-10px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    .animate-fade-in {
      animation: fadeIn 0.3s ease-out;
    }

    .animate-slide-in {
      animation: slideIn 0.3s ease-out;
    }

    /* Улучшенные hover эффекты */
    .hover-lift {
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .hover-lift:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Плавные переходы для карточек */
    .card-transition {
      transition: all 0.2s ease-in-out;
    }

    /* Кастомные скроллбары */
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }

    /* Центрирование контента с учетом боковой панели */
    .admin-content {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    @media (min-width: 768px) {
      .admin-content {
        width: 100%;
        max-width: none;
        margin: 0;
        padding-left: 2rem;
        padding-right: 2rem;
      }
    }

    /* Адаптивное центрирование для больших экранов */
    @media (min-width: 1280px) {
      .admin-content {
        max-width: 1200px;
        margin: 0 auto;
      }
    }

    @media (min-width: 1536px) {
      .admin-content {
        max-width: 1400px;
        margin: 0 auto;
      }
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- Верхняя навигация -->
  <nav class="bg-white shadow-sm border-b border-gray-200 fixed w-full top-0 z-30">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <!-- Мобильная кнопка меню -->
          <button type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" id="mobile-menu-button">
            <span class="sr-only">Открыть главное меню</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>

          <div class="flex items-center ml-4 md:ml-0">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">LB</span>
              </div>
            </div>
            <h1 class="ml-3 text-xl font-semibold text-gray-900">LuxBeton Admin</h1>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Уведомления -->
          <button type="button" class="relative p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">Просмотр уведомлений</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
            </svg>
          </button>

          <!-- Профиль пользователя -->
          <div class="relative">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <svg class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                  </svg>
                </div>
              </div>
              <div class="hidden sm:block">
                <span class="text-sm font-medium text-gray-700">Администратор</span>
              </div>
            </div>
          </div>

          <a href="/admin/logout" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
            Выйти
          </a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Мобильное меню overlay -->
  <div class="fixed inset-0 z-40 md:hidden hidden" id="mobile-menu-overlay">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
  </div>

  <div class="flex pt-16">
    <!-- Боковая панель -->
    <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 sidebar-transition mobile-sidebar" id="sidebar">
      <div class="flex flex-col h-full pt-16 md:pt-5">
        <!-- Мобильная кнопка закрытия -->
        <div class="flex items-center justify-between p-4 md:hidden">
          <span class="text-lg font-semibold text-gray-900">Меню</span>
          <button type="button" class="text-gray-400 hover:text-gray-500" id="close-sidebar">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <nav class="flex-1 px-4 pb-4 space-y-1">
          <a href="/admin" class={`${currentPath === '/admin' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath === '/admin' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
            </svg>
            Главная
          </a>

          <a href="/admin/products" class={`${currentPath.startsWith('/admin/products') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath.startsWith('/admin/products') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
            </svg>
            Товары
          </a>

          <a href="/admin/categories" class={`${currentPath.startsWith('/admin/categories') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath.startsWith('/admin/categories') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
            </svg>
            Категории
          </a>

          <a href="/admin/attributes" class={`${currentPath.startsWith('/admin/attributes') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath.startsWith('/admin/attributes') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
            </svg>
            Атрибуты
          </a>

          <a href="/admin/orders" class={`${currentPath.startsWith('/admin/orders') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath.startsWith('/admin/orders') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5c.414 0 .75-.336.75-.75 0-.231-.035-.454-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
            </svg>
            Заявки
          </a>

          <a href="/admin/data-quality" class={`${currentPath.startsWith('/admin/data-quality') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
            <svg class={`${currentPath.startsWith('/admin/data-quality') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
            </svg>
            Качество данных
          </a>

          <!-- Разделитель -->
          <div class="border-t border-gray-200 my-4"></div>

          <!-- Настройки -->
          <div>
            <div class="text-xs font-semibold text-gray-400 px-3 pt-2 pb-1">Настройки</div>
            <a href="/admin/settings/pages" class={`${currentPath.startsWith('/admin/settings/pages') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors`}>
              <svg class={`${currentPath.startsWith('/admin/settings/pages') ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-5 w-5`} fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0A9 9 0 1112 3a9 9 0 018.25 12.25z" />
              </svg>
              Страницы
            </a>
            <!-- В будущем: добавить другие подпункты (дизайн, SEO, интеграции и т.д.) -->
          </div>

          <!-- Разделитель -->
          <div class="border-t border-gray-200 my-4"></div>

          <!-- Дополнительные ссылки -->
          <a href="/" target="_blank" class="border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors">
            <svg class="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
            </svg>
            Перейти на сайт
          </a>
        </nav>
      </div>
    </aside>

    <!-- Основной контент -->
    <main class="flex-1 min-h-screen">
      <div class="admin-content py-4 sm:py-6 lg:py-8 md:ml-64">
        <slot />
      </div>
    </main>
  </div>

  <!-- JavaScript для мобильного меню -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobile-menu-overlay');
      const closeSidebar = document.getElementById('close-sidebar');

      function openSidebar() {
        if (sidebar) sidebar.classList.add('open');
        if (overlay) overlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
      }

      function closeSidebarFn() {
        if (sidebar) sidebar.classList.remove('open');
        if (overlay) overlay.classList.add('hidden');
        document.body.style.overflow = '';
      }

      mobileMenuButton?.addEventListener('click', openSidebar);
      closeSidebar?.addEventListener('click', closeSidebarFn);
      overlay?.addEventListener('click', closeSidebarFn);

      // Закрытие при изменении размера экрана
      window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) {
          closeSidebarFn();
        }
      });
    });
  </script>

  <!-- Модальное окно подтверждения для всех страниц админ-панели -->
  <ConfirmModal />

  <!-- Toast уведомления -->
  <ToastNotification />

  <!-- Утилиты для модальных окон -->
  <script>
    // Глобальные функции для модальных окон в админ-панели
    window.adminModal = {
      async showConfirm(options = {}) {
        if (window.confirmModal) {
          return await window.confirmModal.show(options);
        } else {
          return confirm(options.message || 'Вы уверены?');
        }
      },

      async confirmDelete(itemName = 'этот элемент', options = {}) {
        return this.showConfirm({
          title: 'Подтверждение удаления',
          message: options.message || `Вы уверены, что хотите удалить ${itemName}? Это действие нельзя отменить.`,
          confirmText: 'Удалить',
          cancelText: 'Отмена',
          confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300',
          ...options
        });
      },

      async showSuccess(message, options = {}) {
        if (window.toastManager) {
          window.toastManager.show({
            message,
            type: 'success',
            title: 'Успех',
            duration: 2000,
            ...options
          });
          return true;
        } else {
          // Fallback к модальному окну
          return this.showConfirm({
            title: 'Успех',
            message,
            confirmText: 'ОК',
            cancelText: '',
            confirmButtonClass: 'bg-green-100 hover:bg-green-200 text-green-700 border border-green-300',
            ...options
          });
        }
      },

      async showError(message, options = {}) {
        if (window.toastManager && !options.useModal) {
          window.toastManager.show({
            message,
            type: 'error',
            title: 'Ошибка',
            duration: 3000,
            ...options
          });
          return true;
        } else {
          // Используем модальное окно для важных ошибок
          return this.showConfirm({
            title: 'Ошибка',
            message,
            confirmText: 'ОК',
            cancelText: '',
            confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300',
            ...options
          });
        }
      },

      async showWarning(message, options = {}) {
        return this.showConfirm({
          title: 'Внимание',
          message,
          confirmText: 'Продолжить',
          cancelText: 'Отмена',
          confirmButtonClass: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border border-yellow-300',
          ...options
        });
      }
    };
  </script>
</body>
</html>
