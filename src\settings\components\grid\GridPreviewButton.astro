---
export interface Props {
  blockId: string;
  editorId: string;
}

const { blockId, editorId } = Astro.props;
---

<div class="grid-preview-button-container" data-block-id={blockId} data-editor-id={editorId}>
  <button 
    type="button"
    id={`grid-preview-btn-${blockId}`}
    class="grid-preview-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors duration-200 flex items-center space-x-2"
    data-block-id={blockId}
    data-editor-id={editorId}
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
    </svg>
    <span>Показать превью Grid</span>
  </button>
  
  <!-- Индикатор состояния -->
  <div class="preview-status mt-2 text-xs text-gray-600" id={`preview-status-${blockId}`}>
    Создайте элементы в редакторе и нажмите кнопку для превью
  </div>
</div>

<style>
  .grid-preview-button-container {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8fafc;
    border: 1px dashed #cbd5e1;
    border-radius: 0.5rem;
    text-align: center;
  }

  .grid-preview-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  .grid-preview-btn:active {
    transform: translateY(0);
  }

  .grid-preview-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .preview-status {
    transition: color 0.2s ease;
  }

  .preview-status.success {
    color: #059669;
  }

  .preview-status.error {
    color: #dc2626;
  }
</style>

<script>
  // Обработчик клика по кнопке превью
  document.addEventListener('click', function(e) {
    if (e.target.closest('.grid-preview-btn')) {
      const button = e.target.closest('.grid-preview-btn');
      const blockId = button.dataset.blockId;
      const editorId = button.dataset.editorId;
      
      handleGridPreview(blockId, editorId);
    }
  });

  async function handleGridPreview(blockId, editorId) {
    const button = document.getElementById(`grid-preview-btn-${blockId}`);
    const status = document.getElementById(`preview-status-${blockId}`);
    
    if (!button || !status) return;

    try {
      // Отключаем кнопку и показываем загрузку
      button.disabled = true;
      button.innerHTML = `
        <svg class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>Загрузка...</span>
      `;
      status.textContent = 'Извлекаем данные из редактора...';
      status.className = 'preview-status mt-2 text-xs text-gray-600';

      // Получаем экземпляр редактора
      const editorInstance = window[`${editorId}_instance`];
      
      if (!editorInstance) {
        throw new Error('Редактор не найден');
      }

      // Получаем данные из редактора
      const editorData = await editorInstance.save();
      
      if (!editorData.blocks || editorData.blocks.length === 0) {
        throw new Error('В редакторе нет элементов для отображения в Grid');
      }

      // Передаем данные в Grid-систему
      if (window.gridSystemManager) {
        window.gridSystemManager.updatePreviewFromEditor(editorData.blocks);
        status.textContent = `Превью обновлено! Найдено ${editorData.blocks.length} элементов`;
        status.className = 'preview-status success mt-2 text-xs';
      } else {
        throw new Error('Grid-система не инициализирована');
      }

    } catch (error) {
      console.error('Ошибка при создании превью:', error);
      status.textContent = `Ошибка: ${error.message}`;
      status.className = 'preview-status error mt-2 text-xs';
    } finally {
      // Восстанавливаем кнопку
      button.disabled = false;
      button.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
        <span>Показать превью Grid</span>
      `;
    }
  }
</script>
