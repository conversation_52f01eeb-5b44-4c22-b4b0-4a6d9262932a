---
import AdminLayout from '@layouts/AdminLayout.astro';
---

<AdminLayout title="Как использовать встраивание контента (Embed)">
  <div class="max-w-4xl mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">📺 Как использовать встраивание контента (Embed)</h1>
    
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
      <h2 class="text-xl font-semibold text-blue-900 mb-3">💡 Важная информация</h2>
      <p class="text-blue-800">
        <strong>Плагин Embed НЕ отображается как кнопка в панели инструментов!</strong><br>
        Он работает автоматически при вставке ссылок на поддерживаемые сервисы.
      </p>
    </div>

    <div class="space-y-8">
      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">🎯 Как это работает</h2>
        <ol class="list-decimal list-inside space-y-3 text-gray-700">
          <li>Создайте блок "Параграф" (нажмите Enter или кнопку "Paragraph")</li>
          <li>Вставьте ссылку на поддерживаемый сервис</li>
          <li>Плагин автоматически преобразует ссылку во встроенный контент</li>
        </ol>
      </section>

      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">🎬 Для блоков типа "Видео"</h2>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 class="font-semibold text-green-900 mb-2">Поддерживаемые сервисы:</h3>
          <ul class="grid grid-cols-2 gap-2 text-green-800">
            <li>• YouTube</li>
            <li>• Vimeo</li>
            <li>• Coub</li>
            <li>• Facebook</li>
            <li>• Instagram</li>
            <li>• Twitter</li>
            <li>• Twitch</li>
          </ul>
        </div>
        <div class="mt-4">
          <h4 class="font-semibold text-gray-900 mb-2">Примеры ссылок:</h4>
          <div class="bg-gray-100 p-3 rounded font-mono text-sm space-y-1">
            <div>https://www.youtube.com/watch?v=dQw4w9WgXcQ</div>
            <div>https://vimeo.com/90509568</div>
            <div>https://coub.com/view/1czcdf</div>
          </div>
        </div>
      </section>

      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">🗺️ Для блоков типа "Карты"</h2>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="font-semibold text-blue-900 mb-2">Поддерживаемые сервисы:</h3>
          <ul class="text-blue-800">
            <li>• Google Maps</li>
            <li>• YouTube (для демонстрации)</li>
          </ul>
        </div>
        <div class="mt-4">
          <h4 class="font-semibold text-gray-900 mb-2">Примеры ссылок:</h4>
          <div class="bg-gray-100 p-3 rounded font-mono text-sm space-y-1">
            <div>https://maps.google.com/maps?q=Moscow</div>
            <div>https://www.google.com/maps/@55.7558,37.6176,10z</div>
            <div>https://goo.gl/maps/example</div>
          </div>
        </div>
      </section>

      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">📝 Пошаговая инструкция</h2>
        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</span>
            <div>
              <h4 class="font-semibold">Откройте редактор блока</h4>
              <p class="text-gray-600">Перейдите к редактированию блока типа "video" или "maps"</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">2</span>
            <div>
              <h4 class="font-semibold">Создайте параграф</h4>
              <p class="text-gray-600">Нажмите кнопку "+" и выберите "Paragraph" или просто нажмите Enter</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">3</span>
            <div>
              <h4 class="font-semibold">Вставьте ссылку</h4>
              <p class="text-gray-600">Скопируйте и вставьте ссылку на видео или карту в параграф</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <span class="flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">4</span>
            <div>
              <h4 class="font-semibold">Готово!</h4>
              <p class="text-gray-600">Плагин автоматически преобразует ссылку во встроенный контент</p>
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">⚠️ Устранение неполадок</h2>
        <div class="space-y-4">
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-semibold text-yellow-900 mb-2">Ссылка не преобразуется автоматически?</h4>
            <ul class="text-yellow-800 space-y-1">
              <li>• Убедитесь, что ссылка поддерживается (см. списки выше)</li>
              <li>• Проверьте, что ссылка вставлена в блок "Paragraph"</li>
              <li>• Попробуйте вставить ссылку заново</li>
            </ul>
          </div>
          
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 class="font-semibold text-red-900 mb-2">Встроенный контент не отображается?</h4>
            <ul class="text-red-800 space-y-1">
              <li>• Проверьте настройки блокировщика рекламы</li>
              <li>• Убедитесь, что сервис доступен в вашем регионе</li>
              <li>• Попробуйте другую ссылку с того же сервиса</li>
            </ul>
          </div>
        </div>
      </section>

      <section>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">🔗 Полезные ссылки</h2>
        <div class="space-y-2">
          <a href="/admin/settings/pages" class="text-blue-600 hover:text-blue-800 underline block">← Вернуться к управлению страницами</a>
          <a href="/admin/test-embed-debug" class="text-blue-600 hover:text-blue-800 underline block">🔧 Диагностика плагина Embed</a>
        </div>
      </section>
    </div>
  </div>
</AdminLayout>
