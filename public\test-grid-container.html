<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест Grid Container</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/css/grid-container.css">

    <!-- EditorJS и его плагины -->
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/header@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/paragraph@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/quote@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/code@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/delimiter@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/table@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/link@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/@editorjs/image@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/editorjs-button@latest"></script>
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        .test-grid-toggle {
            margin-bottom: 20px;
        }
        .test-mode-selector {
            margin-bottom: 20px;
        }
        .test-editor {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            min-height: 300px;
            padding: 20px;
            background: #fafafa;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="test-container">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Тест Grid Container System</h1>

        <!-- Секция управления -->
        <div class="test-section">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Управление Grid-системой</h2>

            <!-- Переключатель Grid -->
            <div class="test-grid-toggle">
                <label class="flex items-center space-x-3 cursor-pointer">
                    <input type="checkbox" id="gridEnabled" name="gridEnabled" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <span class="text-sm font-medium text-gray-900">Включить Grid-систему</span>
                </label>
            </div>

            <!-- Переключатель режимов -->
            <div class="test-mode-selector" id="modeSelector" style="display: none;">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Режим работы</h4>
                <div class="flex space-x-4">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="radio" name="gridMode" value="simple" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                        <span class="text-sm text-gray-700">Простой режим</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="radio" name="gridMode" value="container" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                        <span class="text-sm text-gray-700">Режим контейнеров</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Секция редактора -->
        <div class="test-section">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Редактор</h2>
            <div class="test-editor" id="testEditor">
                <p class="text-gray-500 text-center">Здесь будет EditorJS с Grid Container</p>
                <div class="mt-4 text-center">
                    <button id="addContainerBtn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50" disabled>
                        Добавить Grid Container
                    </button>
                </div>
            </div>
        </div>

        <!-- Секция информации -->
        <div class="test-section">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Информация о состоянии</h2>
            <div id="statusInfo" class="text-sm text-gray-600">
                <p><strong>Grid включен:</strong> <span id="gridStatus">Нет</span></p>
                <p><strong>Режим:</strong> <span id="modeStatus">simple</span></p>
                <p><strong>Активный контейнер:</strong> <span id="activeContainerStatus">Нет</span></p>
                <p><strong>Количество контейнеров:</strong> <span id="containersCount">0</span></p>
            </div>
        </div>

        <!-- Секция тестовых контейнеров -->
        <div class="test-section">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Тестовые контейнеры</h2>
            <div id="testContainers">
                <!-- Здесь будут отображаться тестовые контейнеры -->
            </div>
        </div>
    </div>

    <!-- Подключаем скрипты -->
    <script src="/js/editorjs-tools/grid-container.js"></script>
    <script src="/js/grid-container-manager.js"></script>

    <script>
        // Инициализация тестовой страницы
        document.addEventListener('DOMContentLoaded', function() {
            const gridToggle = document.getElementById('gridEnabled');
            const modeInfo = document.getElementById('modeInfo');
            const addContainerBtn = document.getElementById('addContainerBtn');
            const testContainers = document.getElementById('testContainers');

            // Обработчик переключения Grid
            gridToggle.addEventListener('change', function() {
                const isEnabled = this.checked;
                modeInfo.style.display = isEnabled ? 'block' : 'none';
                addContainerBtn.disabled = !isEnabled;
                updateStatus();
            });

            // Обработчик добавления контейнера
            addContainerBtn.addEventListener('click', function() {
                if (window.GridContainerManager) {
                    createTestContainer();
                } else {
                    alert('Grid Container Manager не загружен');
                }
            });

            // Функция создания тестового контейнера
            function createTestContainer() {
                const containerId = 'test_' + Math.random().toString(36).substr(2, 9);
                const containerElement = document.createElement('div');
                containerElement.className = 'grid-container mb-4';
                containerElement.dataset.containerId = containerId;

                containerElement.innerHTML = `
                    <div class="grid-container-header">
                        <div class="grid-container-title">Test Grid Container</div>
                        <div class="grid-container-controls">
                            <button class="grid-container-tune" title="Настройки контейнера">⚙️</button>
                            <button class="grid-container-delete" title="Удалить контейнер">🗑️</button>
                        </div>
                    </div>
                    <div class="grid-container-items">
                        <div class="grid-container-placeholder">
                            <p>Тестовый контейнер создан</p>
                        </div>
                    </div>
                `;

                // Добавляем обработчики
                containerElement.addEventListener('click', function() {
                    // Деактивируем все контейнеры
                    document.querySelectorAll('.grid-container').forEach(c => c.classList.remove('active'));
                    // Активируем текущий
                    this.classList.add('active');
                    updateStatus();
                });

                containerElement.querySelector('.grid-container-delete').addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm('Удалить контейнер?')) {
                        containerElement.remove();
                        updateStatus();
                    }
                });

                testContainers.appendChild(containerElement);
                updateStatus();
            }

            // Функция обновления статуса
            function updateStatus() {
                const gridEnabled = gridToggle.checked;
                const mode = gridEnabled ? 'container' : 'отключен'; // Автоматический режим
                const activeContainer = document.querySelector('.grid-container.active');
                const containersCount = document.querySelectorAll('.grid-container').length;

                document.getElementById('gridStatus').textContent = gridEnabled ? 'Да' : 'Нет';
                document.getElementById('modeStatus').textContent = mode;
                document.getElementById('activeContainerStatus').textContent = activeContainer ?
                    activeContainer.dataset.containerId : 'Нет';
                document.getElementById('containersCount').textContent = containersCount;
            }

            // Инициализация
            updateStatus();

            console.log('🧪 Тестовая страница Grid Container инициализирована');
        });
    </script>
</body>
</html>
