import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';
import { isAuthenticated } from '../../../utils/auth.js';

// GET - получение списка страниц или конкретной страницы
export async function GET({ request, url }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const pageId = url.searchParams.get('id');
    const settings = await loadPageSettings();
    
    if (pageId) {
      // Возвращаем конкретную страницу
      const page = settings.pages?.find(p => p.id === pageId);
      if (!page) {
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Страница не найдена' 
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify({ 
        success: true, 
        page 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      // Возвращаем список всех страниц
      return new Response(JSON.stringify({ 
        success: true, 
        pages: settings.pages || [] 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Ошибка при получении страниц:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// POST - создание новой страницы
export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { id, template, url, visible = true, blocks = [], seo = {} } = body;

    if (!id || !template || !url) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать id, template и url' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    
    // Проверяем, что страница с таким ID не существует
    if (pages.find(p => p.id === id)) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Страница с таким ID уже существует' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const newPage = {
      id,
      template,
      url,
      visible,
      blocks,
      media: [],
      seo: {
        title: seo.title || { ru: '', en: '' },
        description: seo.description || { ru: '', en: '' },
        keywords: seo.keywords || { ru: '', en: '' }
      }
    };

    pages.push(newPage);
    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Страница создана',
      page: newPage
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при создании страницы:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// PUT - обновление существующей страницы
export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать id страницы' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === id);
    
    if (pageIdx === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Страница не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Обновляем страницу
    pages[pageIdx] = { ...pages[pageIdx], ...updateData };
    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Страница обновлена',
      page: pages[pageIdx]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при обновлении страницы:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// DELETE - удаление страницы
export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { id } = body;

    if (!id) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать id страницы' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const settings = await loadPageSettings();
    let pages = settings.pages || [];
    const pageIdx = pages.findIndex(p => p.id === id);
    
    if (pageIdx === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Страница не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    pages.splice(pageIdx, 1);
    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Страница удалена' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при удалении страницы:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Внутренняя ошибка сервера' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
