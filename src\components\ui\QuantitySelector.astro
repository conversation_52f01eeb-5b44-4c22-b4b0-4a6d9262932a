---
export interface Props {
  id?: string;
  class?: string;
  value?: number;
  min?: number;
  max?: number;
  disabled?: boolean;
  onChange?: string; // JavaScript function name to call on change
}

const {
  id = 'quantity-selector',
  class: className = '',
  value = 1,
  min = 1,
  max = 999,
  disabled = false,
  onChange
} = Astro.props;
---

<div class={`quantity-selector ${className}`} data-quantity-selector>
  <div class="quantity-display">
    <span class="quantity-value" data-quantity-value>{value}</span>
  </div>
  <div class="quantity-controls">
    <button 
      type="button" 
      class="quantity-btn quantity-btn-plus" 
      data-quantity-action="increase"
      disabled={disabled}
      aria-label="Увеличить количество"
    >
      +
    </button>
    <button 
      type="button" 
      class="quantity-btn quantity-btn-minus" 
      data-quantity-action="decrease"
      disabled={disabled}
      aria-label="Уменьшить количество"
    >
      -
    </button>
  </div>
  <input 
    type="hidden" 
    id={id}
    name="quantity" 
    value={value}
    data-quantity-input
  />
</div>

<style>
  .quantity-selector {
    display: inline-flex;
    align-items: stretch;
    position: relative;
    height: 40px;
    border: 1px solid #9ca3af;
    background: white;
    font-family: inherit;
  }

  .quantity-display {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    min-width: 50px;
    background: white;
    border-right: 1px solid #9ca3af;
  }

  .quantity-value {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    user-select: none;
  }

  .quantity-controls {
    display: flex;
    flex-direction: column;
    width: 24px;
  }

  .quantity-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    transition: background-color 0.2s;
    user-select: none;
  }

  .quantity-btn:hover:not(:disabled) {
    background: #e5e7eb;
  }

  .quantity-btn:active:not(:disabled) {
    background: #d1d5db;
  }

  .quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .quantity-btn-plus {
    border-bottom: 1px solid #9ca3af;
  }

  .quantity-btn-minus {
    /* No additional border needed */
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .quantity-selector {
      height: 36px;
    }
    
    .quantity-display {
      min-width: 45px;
      padding: 0 10px;
    }
    
    .quantity-value {
      font-size: 14px;
    }
    
    .quantity-controls {
      width: 22px;
    }
    
    .quantity-btn {
      font-size: 12px;
    }
  }

  input[type=number]::-webkit-outer-spin-button,
  input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
    text-align: center;
    transition: background 0.2s;
  }
  input[type=number]:focus,
  input[type=number]:active {
    background: #e5e7eb;
    outline: none;
  }
  input[type=number]::selection {
    background: #e5e7eb;
    color: inherit;
  }
</style>

<script>
  class QuantitySelector {
    constructor(element) {
      this.element = element;
      this.valueElement = element.querySelector('[data-quantity-value]');
      this.inputElement = element.querySelector('[data-quantity-input]');
      this.increaseBtn = element.querySelector('[data-quantity-action="increase"]');
      this.decreaseBtn = element.querySelector('[data-quantity-action="decrease"]');
      
      this.min = parseInt(this.inputElement.getAttribute('min')) || 1;
      this.max = parseInt(this.inputElement.getAttribute('max')) || 999;
      this.value = parseInt(this.inputElement.value) || 1;
      
      this.init();
    }
    
    init() {
      this.increaseBtn.addEventListener('click', () => this.increase());
      this.decreaseBtn.addEventListener('click', () => this.decrease());
      this.updateDisplay();
    }
    
    increase() {
      if (this.value < this.max) {
        this.setValue(this.value + 1);
      }
    }
    
    decrease() {
      if (this.value > this.min) {
        this.setValue(this.value - 1);
      }
    }
    
    setValue(newValue) {
      const value = Math.max(this.min, Math.min(this.max, parseInt(newValue) || this.min));
      this.value = value;
      this.updateDisplay();
      this.triggerChange();
    }
    
    updateDisplay() {
      this.valueElement.textContent = this.value;
      this.inputElement.value = this.value;
      
      // Update button states
      this.decreaseBtn.disabled = this.value <= this.min;
      this.increaseBtn.disabled = this.value >= this.max;
    }
    
    triggerChange() {
      // Dispatch custom event
      const event = new CustomEvent('quantitychange', {
        detail: { value: this.value },
        bubbles: true
      });
      this.element.dispatchEvent(event);
      
      // Also trigger change on input for form compatibility
      const changeEvent = new Event('change', { bubbles: true });
      this.inputElement.dispatchEvent(changeEvent);
    }
    
    getValue() {
      return this.value;
    }
  }

  // Initialize all quantity selectors on the page
  document.addEventListener('DOMContentLoaded', () => {
    const selectors = document.querySelectorAll('[data-quantity-selector]');
    selectors.forEach(selector => {
      if (!selector.quantitySelector) {
        selector.quantitySelector = new QuantitySelector(selector);
      }
    });
    // Добавляем выделение всего значения при фокусе для всех input[type=number] внутри селектора
    const inputs = document.querySelectorAll('.quantity-selector input[type=number]');
    inputs.forEach(input => {
      input.addEventListener('focus', (e) => {
        e.target.select();
      });
    });
  });

  // Make QuantitySelector available globally for dynamic creation
  window.QuantitySelector = QuantitySelector;
</script>
