/**
 * Утилиты для валидации данных
 */

/**
 * Валидация белорусского номера телефона
 * @param {string} phone - номер телефона
 * @returns {boolean} - true если номер корректный
 */
export function validateBelarusianPhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  // Убираем все символы кроме цифр
  const digitsOnly = phone.replace(/\D/g, '');
  
  // Проверяем белорусские номера
  // Формат: +375 (XX) XXX-XX-XX или 375XXXXXXXXX
  if (digitsOnly.startsWith('375')) {
    // Полный номер с кодом страны: 375 + 2 цифры кода оператора + 7 цифр номера = 12 цифр
    return digitsOnly.length === 12;
  } else if (digitsOnly.length === 9) {
    // Номер без кода страны: 2 цифры кода оператора + 7 цифр номера = 9 цифр
    // Проверяем, что начинается с корректного кода оператора
    const operatorCodes = ['25', '29', '33', '44'];
    return operatorCodes.some(code => digitsOnly.startsWith(code));
  }
  
  return false;
}

/**
 * Валидация имени
 * @param {string} name - имя
 * @returns {boolean} - true если имя корректное
 */
export function validateName(name) {
  if (!name || typeof name !== 'string') {
    return false;
  }
  
  const trimmedName = name.trim();
  return trimmedName.length >= 2 && trimmedName.length <= 100;
}

/**
 * Валидация данных заказа товаров
 * @param {object} orderData - данные заказа
 * @returns {array} - массив ошибок валидации
 */
export function validateProductOrder(orderData) {
  const errors = [];
  
  // Проверка имени клиента
  if (!validateName(orderData.clientName)) {
    errors.push('Некорректное имя клиента (должно быть от 2 до 100 символов)');
  }
  
  // Проверка телефона
  if (!validateBelarusianPhone(orderData.phone)) {
    errors.push('Некорректный номер телефона (должен быть белорусский номер)');
  }
  
  // Проверка товаров
  if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
    errors.push('Заказ должен содержать хотя бы один товар');
  }
  
  // Проверка названия компании для юридических лиц
  if (orderData.clientType === 'legal' && (!orderData.companyName || orderData.companyName.trim().length < 2)) {
    errors.push('Для юридических лиц необходимо указать название компании');
  }
  
  return errors;
}

/**
 * Валидация данных запроса на звонок
 * @param {object} callData - данные запроса
 * @returns {array} - массив ошибок валидации
 */
export function validateCallRequest(callData) {
  const errors = [];
  
  // Проверка имени
  if (!validateName(callData.name)) {
    errors.push('Некорректное имя (должно быть от 2 до 100 символов)');
  }
  
  // Проверка телефона
  if (!validateBelarusianPhone(callData.phone)) {
    errors.push('Некорректный номер телефона (должен быть белорусский номер)');
  }
  
  // Проверка сообщения (опционально)
  if (callData.message && callData.message.length > 1000) {
    errors.push('Сообщение слишком длинное (максимум 1000 символов)');
  }
  
  return errors;
}
