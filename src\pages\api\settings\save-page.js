// API для сохранения страниц (система настроек)
import { loadPageSettings } from '../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../settings/utils/settingsSaver.js';
import { isAuthenticated } from '../../../utils/auth';

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response('Не авторизован', { status: 401 });
    }

    const formData = await request.formData();
    const data = Object.fromEntries(formData.entries());

    // Получаем ID из основного поля или из скрытого поля
    const pageId = data.id || data.page_id;

    // Проверяем наличие ID
    if (!pageId || pageId.trim() === '') {
      return new Response('ID страницы обязателен', { status: 400 });
    }

    // Восстановление структуры из flat-полей
    const pageData = {
      id: pageId.trim(),
      template: data.template,
      visible: data.visible === 'on',
      url: { ru: data['url.ru'] || '', en: data['url.en'] || '' },
      seo: {
        title: { ru: data['seo.title.ru'] || '', en: data['seo.title.en'] || '' },
        description: { ru: data['seo.description.ru'] || '', en: data['seo.description.en'] || '' },
        keywords: { ru: data['seo.keywords.ru'] || '', en: data['seo.keywords.en'] || '' }
      }
    };

    const settings = await loadPageSettings();
    let pages = settings.pages || [];

    // Ищем существующую страницу по ID
    const existingPageIndex = pages.findIndex(p => p.id === pageData.id);

    if (existingPageIndex >= 0) {
      // Обновляем существующую страницу, сохраняя блоки и медиа
      const existingPage = pages[existingPageIndex];
      pages[existingPageIndex] = {
        ...existingPage,
        ...pageData,
        blocks: existingPage.blocks || [],
        media: existingPage.media || []
      };
    } else {
      // Создаем новую страницу только если ID действительно новый
      pages.push({
        ...pageData,
        blocks: [],
        media: []
      });
    }

    settings.pages = pages;
    await savePageSettings(settings);

    return new Response(null, {
      status: 303,
      headers: { Location: '/admin/settings/pages' }
    });

  } catch (error) {
    return new Response('Ошибка сервера при сохранении страницы', { status: 500 });
  }
}
