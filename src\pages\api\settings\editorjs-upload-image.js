// API endpoint для загрузки изображений в EditorJS (система настроек)
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export async function POST({ request }) {
  try {
    console.log('📸 Получен запрос на загрузку изображения для EditorJS (настройки)');

    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Не авторизован'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const formData = await request.formData();
    const file = formData.get('image');

    if (!file) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Файл не найден'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем тип файла
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Неподдерживаемый тип файла. Разрешены: JPEG, PNG, GIF, WebP'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем размер файла (максимум 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return new Response(JSON.stringify({
        success: 0,
        message: 'Файл слишком большой. Максимальный размер: 5MB'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создаем директорию для загрузок настроек если её нет
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'settings');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Генерируем уникальное имя файла
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(file.name);
    const filename = `${timestamp}_${randomString}${extension}`;
    const filepath = path.join(uploadsDir, filename);

    // Сохраняем файл
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    fs.writeFileSync(filepath, buffer);

    console.log(`✅ Изображение настроек сохранено: ${filename}`);

    // Возвращаем ответ в формате EditorJS
    return new Response(JSON.stringify({
      success: 1,
      file: {
        url: `/uploads/settings/${filename}`,
        name: file.name,
        size: file.size
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Ошибка при загрузке изображения настроек:', error);
    
    return new Response(JSON.stringify({
      success: 0,
      message: 'Ошибка сервера при загрузке изображения'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
