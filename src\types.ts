export interface Currency {
  key: string;
  label: {
    ru: string;
  };
  is_custom: boolean;
  simvol: string;
}

export interface Unit {
  key: string;
  label: {
    ru: string;
  };
  is_custom: boolean;
}

export interface SettingsProduct {
  currencies: {
    primary: string;
    supported: Currency[];
  };
  units: {
    [category: string]: {
      primary: string;
      supported: Unit[];
    };
  };
  global_settings?: {
    price_format?: string;
    decimal_separator?: string;
    thousands_separator?: string;
  };
}

export type ProductStatus = 'draft' | 'published' | 'unpublished';

export interface ProductVariant {
  id: string;
  name: string;
  attributes: Record<string, any>;
  price: {
    value: number;
    currency: string;
    unit: string;
    simvol?: string;
  };
  sku?: string;
  isPrimaryPrice?: boolean;
  inStock?: boolean;
}

export interface Product {
  id: string;
  sku: string;
  name: string;
  slug: string;
  category: string;
  categorySlug: string;
  subcategory: string;
  shortDescription: string;
  fullDescription: string;
  basePrice?: {
    value: number;
    unit: string;
    simvol?: string;
    currency?: string;
  } | null;
  attributes: Record<string, any>;
  variants?: ProductVariant[];
  images: {
    main: string;
    additional: string[];
  };
  inStock: boolean;
  popularity: number;
  rating?: number;
  reviews?: number;
  status: ProductStatus;
  createdAt?: string;
  updatedAt?: string;
}
