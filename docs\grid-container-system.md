# Система Grid Container

## Обзор

Система Grid Container позволяет группировать элементы EditorJS в контейнеры для более гибкого управления grid-системой. Вместо применения настроек к каждому элементу отдельно, настройки применяются ко всему контейнеру.

## Режим работы

Система автоматически работает в **режиме контейнеров**:
- Элементы группируются в контейнеры
- Настройки grid применяются к контейнерам
- Более гибкое управление компоновкой
- Инструмент "Grid Container" всегда доступен в EditorJS

## Использование

### Активация системы контейнеров

1. Откройте редактор блока
2. Включите Grid-систему
3. Система автоматически переключается в режим контейнеров
4. Инструмент "Grid Container" готов к использованию

### Создание контейнера

1. Нажмите "+" в EditorJS
2. Выберите "Grid Container"
3. Если Grid-система включена: создается контейнер с серой обводкой
4. Если Grid-система выключена: показывается предупреждение с инструкцией
5. При активации контейнера обводка становится синей

### Добавление элементов в контейнер

1. Активируйте контейнер (нажмите на него)
2. Нажмите "+" в EditorJS
3. Выберите любой инструмент (текст, изображение, кнопка и т.д.)
4. Элемент автоматически добавится в активный контейнер

### Переключение между контейнерами

1. Нажмите на любую область внутри контейнера
2. Контейнер станет активным (синяя обводка)
3. Новые элементы будут добавляться в этот контейнер

### Создание нового контейнера

1. Нажмите "+" в EditorJS
2. Выберите "Grid Container"
3. Предыдущий контейнер деактивируется
4. Новый контейнер становится активным

## Визуальные индикаторы

### Обводка контейнера
- **Серая линия**: Неактивный контейнер
- **Синяя линия**: Активный контейнер для редактирования

### Элементы управления
- **⚙️**: Настройки контейнера (Click to tune)
- **🗑️**: Удаление контейнера
- **↑/↓**: Перемещение элементов внутри контейнера

### Смещение элементов
- Элементы "+" и "Click to tune" смещаются при активном контейнере
- Обеспечивает лучшую видимость границ контейнера

## Технические детали

### Структура данных

```typescript
interface GridContainer {
  id: string;
  order: number;
  isActive: boolean;
  items: GridItem[];
  gridColumn?: string;
  gridRow?: string;
  settings?: {
    padding?: { value: number; unit: string };
    margin?: { value: number; unit: string };
    backgroundColor?: string;
    border?: {
      width: number;
      style: string;
      color: string;
    };
  };
}
```

### Настройки Grid-системы

```typescript
interface GridSystemSettings {
  enabled: boolean;
  mode: 'simple' | 'container';

  // Для режима simple
  items?: GridItem[];

  // Для режима container
  containers?: GridContainer[];
  activeContainerId?: string;

  // Общие настройки
  columns: number;
  gap: { value: number; unit: string };
  // ... другие настройки
}
```

## Файлы системы

### JavaScript
- `/public/js/editorjs-tools/grid-container.js` - Инструмент EditorJS
- `/public/js/grid-container-manager.js` - Менеджер контейнеров

### CSS
- `/public/css/grid-container.css` - Стили контейнеров

### Компоненты
- `CompactGridManager.astro` - Обновлен для поддержки контейнеров
- `AdvancedEditorJS.astro` - Интеграция с Grid Container

### Типы
- `src/settings/types.ts` - Обновлены интерфейсы

## API

### GridContainerManager

```javascript
// Создание контейнера
const containerId = window.GridContainerManager.createContainer();

// Получение активного контейнера
const activeContainer = window.GridContainerManager.getActiveContainer();

// Получение всех контейнеров
const containers = window.GridContainerManager.getContainersData();

// Экспорт настроек
const settings = window.GridContainerManager.exportForGridSystem();
```

### События

```javascript
// Активация контейнера
document.addEventListener('gridContainerActivated', (e) => {
  console.log('Активирован контейнер:', e.detail.containerId);
});

// Обновление контейнеров
document.addEventListener('gridContainerUpdate', (e) => {
  console.log('Обновлены контейнеры:', e.detail.containers);
});
```

## Тестирование

Для тестирования системы откройте:
`/test-grid-container.html`

Эта страница позволяет:
- Переключать режимы Grid-системы
- Создавать тестовые контейнеры
- Проверять состояние системы
- Тестировать визуальные элементы

## Совместимость

Система полностью совместима с существующей grid-системой:
- Простой режим работает как раньше
- Данные сохраняются в том же формате
- Можно переключаться между режимами

## Ограничения

1. Grid Container всегда доступен в меню, но функционален только при включенной grid-системе
2. В одном блоке может быть активен только один контейнер
3. Элементы нельзя перемещать между контейнерами через UI (только программно)
4. Система автоматически работает в режиме контейнеров (нет ручного переключения)

## Будущие улучшения

1. Drag & Drop для перемещения элементов между контейнерами
2. Настройки стилей для отдельных контейнеров
3. Шаблоны контейнеров
4. Копирование и вставка контейнеров
5. Вложенные контейнеры
